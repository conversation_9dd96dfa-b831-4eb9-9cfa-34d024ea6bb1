<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Nurse Login - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-user-nurse me-2"></i>
                            Nurse Login Test
                        </h2>
                        <p class="mb-0 mt-2">Test the nurse login functionality</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Nurse Login Test Instructions</h5>
                            <p class="mb-3">Use these credentials to test the nurse login system:</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Primary Test Account:</h6>
                                    <ul>
                                        <li><strong>Nurse ID:</strong> NUR001</li>
                                        <li><strong>Password:</strong> nurse123</li>
                                        <li><strong>Name:</strong> Nurse Amina Suleiman</li>
                                        <li><strong>Department:</strong> General Ward</li>
                                        <li><strong>Shift:</strong> Day Shift</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Other Available Accounts:</h6>
                                    <ul>
                                        <li><strong>NUR002</strong> - Nurse Blessing Okoro (Pediatric Ward)</li>
                                        <li><strong>NUR003</strong> - Nurse Grace Adamu (Emergency Ward)</li>
                                        <li><strong>NUR004</strong> - Nurse Mary Johnson (Surgical Ward)</li>
                                        <li><strong>NUR005</strong> - Nurse Khadija Usman (Maternity Ward)</li>
                                    </ul>
                                    <small class="text-muted">All use password: <strong>nurse123</strong></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-play me-2"></i>Quick Test</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Test the login system directly:</p>
                                        <div class="d-grid">
                                            <a href="nurse-login.html" class="btn btn-info">
                                                <i class="fas fa-sign-in-alt me-2"></i>Go to Nurse Login
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Test Functions</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Test login functions programmatically:</p>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-success btn-sm" onclick="testValidation()">
                                                Test Validation
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="testCredentials()">
                                                Test Credentials
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" onclick="showNurseList()">
                                                Show All Nurses
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                            <div id="testResults" class="border rounded p-3 bg-light">
                                <p class="text-muted mb-0">Click the test buttons above to see results...</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-list-check me-2"></i>Test Checklist</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test1">
                                            <label for="test1">Nurse login page loads correctly</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test2">
                                            <label for="test2">Sample credentials are displayed</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test3">
                                            <label for="test3">Login with NUR001/nurse123 works</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test4">
                                            <label for="test4">Invalid credentials show error</label>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test5">
                                            <label for="test5">Success message appears on login</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test6">
                                            <label for="test6">Redirects to nurse dashboard</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test7">
                                            <label for="test7">Session data is stored correctly</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test8">
                                            <label for="test8">All 5 nurse accounts work</label>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-users me-2"></i>Complete Nurse Directory</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nurse ID</th>
                                            <th>Name</th>
                                            <th>Department</th>
                                            <th>Shift</th>
                                            <th>Experience</th>
                                            <th>Specialization</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>NUR001</strong></td>
                                            <td>Nurse Amina Suleiman</td>
                                            <td>General Ward</td>
                                            <td>Day Shift</td>
                                            <td>5 years</td>
                                            <td>General Nursing</td>
                                        </tr>
                                        <tr>
                                            <td><strong>NUR002</strong></td>
                                            <td>Nurse Blessing Okoro</td>
                                            <td>Pediatric Ward</td>
                                            <td>Night Shift</td>
                                            <td>7 years</td>
                                            <td>Pediatric Nursing</td>
                                        </tr>
                                        <tr>
                                            <td><strong>NUR003</strong></td>
                                            <td>Nurse Grace Adamu</td>
                                            <td>Emergency Ward</td>
                                            <td>Day Shift</td>
                                            <td>9 years</td>
                                            <td>Emergency Nursing</td>
                                        </tr>
                                        <tr>
                                            <td><strong>NUR004</strong></td>
                                            <td>Nurse Mary Johnson</td>
                                            <td>Surgical Ward</td>
                                            <td>Day Shift</td>
                                            <td>3 years</td>
                                            <td>Surgical Nursing</td>
                                        </tr>
                                        <tr>
                                            <td><strong>NUR005</strong></td>
                                            <td>Nurse Khadija Usman</td>
                                            <td>Maternity Ward</td>
                                            <td>Night Shift</td>
                                            <td>11 years</td>
                                            <td>Maternity Nursing</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-outline-primary me-3">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                            <a href="test-all-features.html" class="btn btn-outline-info">
                                <i class="fas fa-cogs me-2"></i>All System Tests
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/nurse-login.js"></script>
    <script>
        function testValidation() {
            const results = document.getElementById('testResults');
            
            // Test the validation function
            const testCases = [
                { id: 'NUR001', password: 'nurse123', expected: true },
                { id: 'NUR002', password: 'nurse123', expected: true },
                { id: 'NUR001', password: 'wrong', expected: false },
                { id: 'INVALID', password: 'nurse123', expected: false },
                { id: '', password: 'nurse123', expected: false }
            ];
            
            let html = '<h6>Validation Test Results:</h6><ul>';
            
            testCases.forEach(test => {
                const result = validateNurseCredentials(test.id, test.password);
                const status = result === test.expected ? '✅' : '❌';
                html += `<li>${status} ${test.id}/${test.password} - Expected: ${test.expected}, Got: ${result}</li>`;
            });
            
            html += '</ul>';
            results.innerHTML = html;
        }
        
        function testCredentials() {
            const results = document.getElementById('testResults');
            const nurses = getAllNurses();
            
            let html = '<h6>Available Nurse Credentials:</h6><div class="table-responsive"><table class="table table-sm"><thead><tr><th>ID</th><th>Name</th><th>Department</th><th>Status</th></tr></thead><tbody>';
            
            Object.keys(nurses).forEach(id => {
                const nurse = nurses[id];
                html += `<tr><td>${id}</td><td>${nurse.name}</td><td>${nurse.department}</td><td><span class="badge bg-success">${nurse.status}</span></td></tr>`;
            });
            
            html += '</tbody></table></div>';
            results.innerHTML = html;
        }
        
        function showNurseList() {
            const results = document.getElementById('testResults');
            const nurses = getAllNurses();
            
            let html = '<h6>Complete Nurse Information:</h6>';
            
            Object.keys(nurses).forEach(id => {
                const nurse = nurses[id];
                html += `
                    <div class="card mb-2">
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>${nurse.name}</strong> (${id})<br>
                                    <small>Department: ${nurse.department} | Shift: ${nurse.shift}</small>
                                </div>
                                <div class="col-md-6">
                                    <small>Email: ${nurse.email}<br>
                                    Phone: ${nurse.phone}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            results.innerHTML = html;
        }
    </script>
</body>
</html>
