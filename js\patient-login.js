// Patient Login JavaScript

// Load patient credentials from both default and registered patients
function loadPatientCredentials() {
    // Default patient credentials
    const defaultPatients = {
        'PT001': {
            password: 'patient123',
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1990-05-15'
        },
        'PT002': {
            password: 'patient456',
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1985-08-22'
        },
        'PT003': {
            password: 'patient789',
            name: '<PERSON>',
            email: 'micha<PERSON>.<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1992-12-10'
        },
        'PT004': {
            password: 'patient321',
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1988-03-18'
        },
        'PT005': {
            password: 'patient654',
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1995-07-25'
        },
        'PT006': {
            password: 'patient987',
            name: 'Emily <PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1991-11-08'
        },
        'PT007': {
            password: 'patient147',
            name: 'Robert <PERSON>',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1987-09-14'
        },
        'PT008': {
            password: 'patient258',
            name: 'Lisa Anderson',
            email: '<EMAIL>',
            phone: '+234-************',
            dateOfBirth: '1993-04-30'
        }
    };

    // Load registered patients from localStorage
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};

    // Merge default and registered patients
    return { ...defaultPatients, ...registeredPatients };
}

// Get current patient credentials
const patientCredentials = loadPatientCredentials();

document.addEventListener('DOMContentLoaded', function() {
    initializePatientLogin();
});

function initializePatientLogin() {
    const loginForm = document.getElementById('patientLoginForm');
    const togglePasswordBtn = document.getElementById('togglePassword');
    
    // Handle form submission
    if (loginForm) {
        loginForm.addEventListener('submit', handlePatientLogin);
    }
    
    // Handle password toggle
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            togglePasswordVisibility('loginPassword', 'togglePassword');
        });
    }
    
    // Check if already logged in
    checkExistingSession();
}

function handlePatientLogin(event) {
    event.preventDefault();
    console.log('Student login form submitted');

    const form = event.target;
    const formData = new FormData(form);
    const matricNumber = formData.get('matricNumber').trim().toUpperCase();
    const password = formData.get('loginPassword').trim();
    const rememberMe = formData.get('rememberMe');

    console.log('Matric Number:', matricNumber);
    console.log('Password:', password);
    console.log('Available credentials:', Object.keys(patientCredentials));

    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        showErrorAlert('Please fill in all required fields.');
        return;
    }

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);

    // Simulate API call delay
    setTimeout(() => {
        if (validatePatientCredentials(matricNumber, password)) {
            // Successful login - reload credentials to get latest data
            const currentCredentials = loadPatientCredentials();
            const patientInfo = currentCredentials[matricNumber];

            const patientData = {
                id: matricNumber,
                name: patientInfo.name,
                email: patientInfo.email,
                phone: patientInfo.phone,
                dateOfBirth: patientInfo.dateOfBirth,
                // Include additional fields if they exist (for newly registered patients)
                firstName: patientInfo.firstName || patientInfo.name.split(' ')[0],
                lastName: patientInfo.lastName || patientInfo.name.split(' ').slice(1).join(' '),
                gender: patientInfo.gender || 'Not specified',
                bloodGroup: patientInfo.bloodGroup || 'Unknown',
                address: patientInfo.address || 'Not provided',
                city: patientInfo.city || 'Not provided',
                state: patientInfo.state || 'Not provided',
                emergencyContactName: patientInfo.emergencyContactName || 'Not provided',
                emergencyContactPhone: patientInfo.emergencyContactPhone || 'Not provided',
                role: 'patient',
                loginTime: new Date().toISOString()
            };

            // Store user data
            const storage = rememberMe ? localStorage : sessionStorage;
            storage.setItem('hospitalUser', JSON.stringify(patientData));

            // Show success message
            showSuccessAlert('Login successful! Redirecting to patient dashboard...');

            // Redirect to patient dashboard
            setTimeout(() => {
                window.location.href = 'patient-dashboard.html';
            }, 1500);

        } else {
            // Failed login
            console.log('Login failed for:', patientId);
            showErrorAlert('Invalid Patient ID or password. Please try again.');
            form.classList.add('was-validated');
        }

        hideLoading();
    }, 1000);
}

function validatePatientCredentials(patientId, password) {
    // Reload credentials to include newly registered patients
    const currentCredentials = loadPatientCredentials();
    return currentCredentials[patientId] && currentCredentials[patientId].password === password;
}

function checkExistingSession() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (userData) {
        const user = JSON.parse(userData);
        if (user.role === 'patient') {
            // User is already logged in as patient
            showSuccessAlert('You are already logged in. Redirecting...');
            setTimeout(() => {
                window.location.href = 'patient-dashboard.html';
            }, 1500);
        }
    }
}

function resetPassword() {
    const patientId = document.getElementById('resetPatientId').value.trim().toUpperCase();
    const email = document.getElementById('resetEmail').value.trim();

    if (!patientId || !email) {
        showErrorAlert('Please fill in all fields.');
        return;
    }

    if (!validateEmail(email)) {
        showErrorAlert('Please enter a valid email address.');
        return;
    }

    // Reload credentials to include newly registered patients
    const currentCredentials = loadPatientCredentials();

    // Check if patient exists
    if (!currentCredentials[patientId]) {
        showErrorAlert('Patient ID not found.');
        return;
    }

    // Check if email matches
    if (currentCredentials[patientId].email !== email) {
        showErrorAlert('Email does not match our records.');
        return;
    }

    // Generate reset token
    const resetToken = generateResetToken();
    const resetExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Store reset token
    const resetTokens = JSON.parse(localStorage.getItem('passwordResetTokens') || '[]');
    resetTokens.push({
        patientId: patientId,
        email: email,
        token: resetToken,
        expiry: resetExpiry.toISOString(),
        used: false,
        createdAt: new Date().toISOString()
    });
    localStorage.setItem('passwordResetTokens', JSON.stringify(resetTokens));

    // Create reset link
    const resetLink = `${window.location.origin}${window.location.pathname.replace('patient-login.html', '')}reset-password.html?token=${resetToken}`;

    // Show reset email modal with link
    showResetEmailModal(currentCredentials[patientId].name, resetLink, email);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
    if (modal) {
        modal.hide();
    }

    // Clear form
    document.getElementById('forgotPasswordForm').reset();
}

// Utility functions
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function showSuccessAlert(message) {
    const alert = document.getElementById('successAlert');
    const messageSpan = document.getElementById('successMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showErrorAlert(message) {
    const alert = document.getElementById('errorAlert');
    const messageSpan = document.getElementById('errorMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function generateResetToken() {
    return 'reset_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
}

function showResetEmailModal(patientName, resetLink, email) {
    // Create email preview modal
    const emailModalHTML = `
        <div class="modal fade" id="resetEmailModal" tabindex="-1" aria-labelledby="resetEmailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title" id="resetEmailModalLabel">
                            <i class="fas fa-envelope me-2"></i>Password Reset Email Sent
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Reset Link Sent Successfully!</h6>
                            <p class="mb-0">A password reset link has been sent to <strong>${email}</strong></p>
                        </div>

                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-envelope me-2"></i>Email Preview (For Testing)</h6>
                            </div>
                            <div class="card-body">
                                <div class="email-content">
                                    <h5>Password Reset Request - Newgate Hospital</h5>
                                    <p>Dear ${patientName},</p>
                                    <p>We received a request to reset your password for your Newgate Hospital patient account.</p>
                                    <p>Click the link below to reset your password:</p>
                                    <div class="p-3 bg-light border rounded text-center">
                                        <a href="${resetLink}" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-key me-2"></i>Reset My Password
                                        </a>
                                    </div>
                                    <p class="mt-3"><strong>Important:</strong></p>
                                    <ul>
                                        <li>This link will expire in 24 hours</li>
                                        <li>If you didn't request this reset, please ignore this email</li>
                                        <li>For security, this link can only be used once</li>
                                    </ul>
                                    <p>Best regards,<br>Newgate Hospital Team</p>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle me-2"></i>For Testing Purposes:</h6>
                            <p class="mb-2">Since this is a demo, you can directly click the reset link above to test the password reset functionality.</p>
                            <p class="mb-0">In a real application, this would be sent to your email address.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="${resetLink}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Test Reset Link
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('resetEmailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', emailModalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('resetEmailModal'));
    modal.show();
}

// Make functions globally available
window.resetPassword = resetPassword;
window.generateResetToken = generateResetToken;
window.showResetEmailModal = showResetEmailModal;
