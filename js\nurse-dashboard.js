// Nurse Dashboard JavaScript - Newgate Hospital Management System

let currentNurse = null;

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeNurseDashboard();
});

function initializeNurseDashboard() {
    // Check if user is logged in as nurse
    const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
    
    if (!user.role || user.role !== 'nurse') {
        // Redirect to login if not logged in as nurse
        window.location.href = 'nurse-login.html';
        return;
    }
    
    currentNurse = user;
    
    // Update UI with nurse information
    updateNurseInfo();
    
    // Set current date
    updateCurrentDate();
    
    // Load dashboard data
    loadDashboardData();

    // Initialize profile picture manager
    profilePictureManager.initializeProfilePicture(currentNurse.id, 'nurse');

    // Show dashboard by default
    showDashboard();
}

function updateNurseInfo() {
    if (currentNurse) {
        document.getElementById('nurseName').textContent = currentNurse.name;
        document.getElementById('welcomeName').textContent = currentNurse.name;
        document.getElementById('nurseDepartment').textContent = currentNurse.department;
        document.getElementById('nurseShift').textContent = currentNurse.shift;
    }
}

function updateCurrentDate() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', options);
}

function loadDashboardData() {
    // Load statistics and update dashboard cards
    const stats = getNurseStats();
    
    // Update stat cards
    document.getElementById('totalPatients').textContent = stats.totalPatients;
    document.getElementById('medicationsDue').textContent = stats.medicationsDue;
    document.getElementById('vitalsNeeded').textContent = stats.vitalsNeeded;
    document.getElementById('tasksCompleted').textContent = stats.tasksCompleted;
}

function getNurseStats() {
    // Get real data from localStorage and generate nurse-specific stats
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const totalStudents = Object.keys(registeredPatients).length;
    
    // Generate department-specific patient assignments
    const departmentPatients = Math.min(Math.floor(totalStudents / 5) + Math.floor(Math.random() * 8) + 5, totalStudents);
    
    return {
        totalPatients: departmentPatients,
        medicationsDue: Math.floor(Math.random() * 10) + 3, // 3-12 medications
        vitalsNeeded: Math.floor(Math.random() * 8) + 2, // 2-9 vitals
        tasksCompleted: Math.floor(Math.random() * 20) + 10 // 10-29 tasks
    };
}

// Section management functions
function showSection(sectionName) {
    // Hide all content sections
    const sections = ['dashboardContent', 'patientsContent', 'wardManagementContent', 
                     'medicationsContent', 'vitalSignsContent', 'shiftReportsContent', 
                     'scheduleContent', 'messagesContent'];
    
    sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
            element.style.display = 'none';
        }
    });
    
    // Show the requested section
    const targetSection = document.getElementById(sectionName + 'Content');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Update sidebar active state
    updateSidebarActive(sectionName);
}

function updateSidebarActive(activeSection) {
    // Remove active class from all sidebar links
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => link.classList.remove('active'));
    
    // Add active class to current section
    const activeLinkMap = {
        'dashboard': 0,
        'patients': 1,
        'wardManagement': 2,
        'medications': 3,
        'vitalSigns': 4,
        'shiftReports': 5,
        'schedule': 6,
        'messages': 7
    };
    
    const linkIndex = activeLinkMap[activeSection];
    if (linkIndex !== undefined && sidebarLinks[linkIndex]) {
        sidebarLinks[linkIndex].classList.add('active');
    }
}

// Navigation functions
function showDashboard() {
    console.log('Loading Dashboard...');
    showSection('dashboard');
    loadDashboardData();
}

function showPatients() {
    console.log('Loading My Patients...');
    showSection('patients');
    loadPatientsData();
}

function showWardManagement() {
    console.log('Loading Ward Management...');
    showSection('wardManagement');
    loadWardManagementData();
}

function showMedications() {
    console.log('Loading Medications...');
    showSection('medications');
    loadMedicationsData();
}

function showVitalSigns() {
    console.log('Loading Vital Signs...');
    showSection('vitalSigns');
    loadVitalSignsData();
}

function showShiftReports() {
    console.log('Loading Shift Reports...');
    showSection('shiftReports');
    loadShiftReportsData();
}

function showSchedule() {
    console.log('Loading Schedule...');
    showSection('schedule');
    loadScheduleData();
}

function showMessages() {
    console.log('Loading Messages...');
    showSection('messages');
    loadMessagesData();
}

// Quick action functions
function recordVitals() {
    showRecordVitalsModal();
}

function administerMedication() {
    showMedicationModal();
}

function updatePatientNotes() {
    showPatientNotesModal();
}

function createShiftReport() {
    showShiftReportModal();
}

// Profile and settings functions
function showProfile() {
    showNurseProfileModal();
}

function showSettings() {
    showNurseSettingsModal();
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        sessionStorage.removeItem('hospitalUser');
        localStorage.removeItem('hospitalUser');
        window.location.href = 'nurse-login.html';
    }
}

// Utility functions
function showSuccessAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function showErrorAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showPatients = showPatients;
window.showWardManagement = showWardManagement;
window.showMedications = showMedications;
window.showVitalSigns = showVitalSigns;
window.showShiftReports = showShiftReports;
window.showSchedule = showSchedule;
window.showMessages = showMessages;
window.recordVitals = recordVitals;
window.administerMedication = administerMedication;
window.updatePatientNotes = updatePatientNotes;
window.createShiftReport = createShiftReport;
window.showProfile = showProfile;
window.showSettings = showSettings;
window.logout = logout;

// Data loading functions
function loadPatientsData() {
    const patientsContainer = document.getElementById('patientsContent');
    if (!patientsContainer) return;

    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const assignedPatients = getAssignedPatients(registeredPatients);

    let html = `
        <h3 class="mb-4">My Patients</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" placeholder="Search patients..." id="patientSearch">
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="addPatientCare()">
                    <i class="fas fa-plus me-2"></i>Add Care Note
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Matric Number</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Room</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    if (assignedPatients.length === 0) {
        html += '<tr><td colspan="6" class="text-center">No patients assigned</td></tr>';
    } else {
        assignedPatients.forEach(patient => {
            const status = getPatientStatus();
            const room = generateRoomNumber();

            html += `
                <tr>
                    <td><strong>${patient.matricNumber}</strong></td>
                    <td>${patient.name}</td>
                    <td>${patient.department || 'N/A'}</td>
                    <td>${room}</td>
                    <td><span class="badge bg-${status.class}">${status.text}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="viewPatientDetails('${patient.matricNumber}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success me-1" onclick="recordVitalsFor('${patient.matricNumber}')">
                            <i class="fas fa-heartbeat"></i>
                        </button>
                        <button class="btn btn-sm btn-warning me-1" onclick="giveMedicationTo('${patient.matricNumber}')">
                            <i class="fas fa-pills"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="updateNotesFor('${patient.matricNumber}')">
                            <i class="fas fa-notes-medical"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    }

    html += '</tbody></table></div>';
    patientsContainer.innerHTML = html;
}

function loadWardManagementData() {
    const wardContainer = document.getElementById('wardManagementContent');
    if (!wardContainer) return;

    const wardInfo = getWardInfo();

    let html = `
        <h3 class="mb-4">Ward Management - ${currentNurse.department}</h3>
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${wardInfo.totalBeds}</h4>
                        <p class="mb-0">Total Beds</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${wardInfo.occupiedBeds}</h4>
                        <p class="mb-0">Occupied</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${wardInfo.availableBeds}</h4>
                        <p class="mb-0">Available</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${wardInfo.occupancyRate}%</h4>
                        <p class="mb-0">Occupancy Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Bed Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
    `;

    for (let i = 1; i <= wardInfo.totalBeds; i++) {
        const isOccupied = i <= wardInfo.occupiedBeds;
        const bedClass = isOccupied ? 'bg-danger' : 'bg-success';
        const bedStatus = isOccupied ? 'Occupied' : 'Available';
        const patientInfo = isOccupied ? generatePatientForBed() : '';

        html += `
            <div class="col-md-4 col-lg-3 mb-3">
                <div class="card border-${isOccupied ? 'danger' : 'success'}">
                    <div class="card-body text-center">
                        <h6>Bed ${i}</h6>
                        <span class="badge ${bedClass} text-white">${bedStatus}</span>
                        ${patientInfo ? `<p class="mt-2 mb-0 small">${patientInfo}</p>` : ''}
                        <div class="mt-2">
                            ${isOccupied ?
                                `<button class="btn btn-sm btn-outline-primary" onclick="viewBedDetails(${i})">View</button>` :
                                `<button class="btn btn-sm btn-outline-success" onclick="assignBed(${i})">Assign</button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    html += '</div></div></div>';
    wardContainer.innerHTML = html;
}

function loadMedicationsData() {
    const medicationsContainer = document.getElementById('medicationsContent');
    if (!medicationsContainer) return;

    const medications = generateMedicationSchedule();

    let html = `
        <h3 class="mb-4">Medication Administration</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="medicationFilter">
                    <option value="">All Medications</option>
                    <option value="due">Due Now</option>
                    <option value="overdue">Overdue</option>
                    <option value="completed">Completed</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-success" onclick="administerMedication()">
                    <i class="fas fa-pills me-2"></i>Administer Medication
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Patient</th>
                        <th>Medication</th>
                        <th>Dosage</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    medications.forEach(med => {
        const statusClass = {
            'Due': 'bg-warning',
            'Overdue': 'bg-danger',
            'Completed': 'bg-success'
        }[med.status] || 'bg-secondary';

        html += `
            <tr>
                <td>${med.time}</td>
                <td>${med.patient}</td>
                <td>${med.medication}</td>
                <td>${med.dosage}</td>
                <td><span class="badge ${statusClass}">${med.status}</span></td>
                <td>
                    ${med.status !== 'Completed' ?
                        `<button class="btn btn-sm btn-success me-1" onclick="administerMed('${med.id}')">
                            <i class="fas fa-check"></i> Give
                        </button>` : ''
                    }
                    <button class="btn btn-sm btn-info" onclick="viewMedDetails('${med.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    medicationsContainer.innerHTML = html;
}

function loadVitalSignsData() {
    const vitalsContainer = document.getElementById('vitalSignsContent');
    if (!vitalsContainer) return;

    const vitals = generateVitalSignsSchedule();

    let html = `
        <h3 class="mb-4">Vital Signs Monitoring</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="date" class="form-control" id="vitalsDate" value="${new Date().toISOString().split('T')[0]}">
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="recordVitals()">
                    <i class="fas fa-heartbeat me-2"></i>Record Vitals
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Patient</th>
                        <th>BP</th>
                        <th>Pulse</th>
                        <th>Temp</th>
                        <th>Resp</th>
                        <th>O2 Sat</th>
                        <th>Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    vitals.forEach(vital => {
        html += `
            <tr>
                <td>${vital.patient}</td>
                <td>${vital.bp || '-'}</td>
                <td>${vital.pulse || '-'}</td>
                <td>${vital.temp || '-'}</td>
                <td>${vital.resp || '-'}</td>
                <td>${vital.o2sat || '-'}</td>
                <td>${vital.time}</td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="updateVitals('${vital.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewVitalHistory('${vital.id}')">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    vitalsContainer.innerHTML = html;
}

function loadShiftReportsData() {
    const reportsContainer = document.getElementById('shiftReportsContent');
    if (!reportsContainer) return;

    let html = `
        <h3 class="mb-4">Shift Reports</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="shiftFilter">
                    <option value="">All Shifts</option>
                    <option value="current">Current Shift</option>
                    <option value="previous">Previous Shift</option>
                    <option value="today">Today</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-success" onclick="createShiftReport()">
                    <i class="fas fa-plus me-2"></i>Create Report
                </button>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h5>Current Shift Summary</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <p><strong>Shift:</strong> ${currentNurse.shift}</p>
                        <p><strong>Department:</strong> ${currentNurse.department}</p>
                        <p><strong>Patients Assigned:</strong> ${getNurseStats().totalPatients}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Medications Given:</strong> ${Math.floor(Math.random() * 15) + 5}</p>
                        <p><strong>Vitals Recorded:</strong> ${Math.floor(Math.random() * 20) + 10}</p>
                        <p><strong>Notes Updated:</strong> ${Math.floor(Math.random() * 8) + 3}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    reportsContainer.innerHTML = html;
}

function loadScheduleData() {
    const scheduleContainer = document.getElementById('scheduleContent');
    if (!scheduleContainer) return;

    let html = `
        <h3 class="mb-4">My Schedule</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="date" class="form-control" id="scheduleDate" value="${new Date().toISOString().split('T')[0]}">
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h5>${currentNurse.shift} Schedule</h5>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-time">08:00</div>
                        <div class="timeline-content">
                            <h6>Shift Handover</h6>
                            <p>Receive report from previous shift</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">09:00</div>
                        <div class="timeline-content">
                            <h6>Morning Rounds</h6>
                            <p>Check on all assigned patients</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">11:00</div>
                        <div class="timeline-content">
                            <h6>Medication Round</h6>
                            <p>Administer scheduled medications</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">14:00</div>
                        <div class="timeline-content">
                            <h6>Vital Signs Check</h6>
                            <p>Record vital signs for all patients</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    scheduleContainer.innerHTML = html;
}

function loadMessagesData() {
    const messagesContainer = document.getElementById('messagesContent');
    if (!messagesContainer) return;

    let html = `
        <h3 class="mb-4">Messages</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="messageFilter">
                    <option value="">All Messages</option>
                    <option value="unread">Unread</option>
                    <option value="doctors">From Doctors</option>
                    <option value="admin">From Admin</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="composeMessage()">
                    <i class="fas fa-plus me-2"></i>Compose Message
                </button>
            </div>
        </div>
        <div class="list-group">
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Patient Care Update Required</h6>
                    <small>2 hours ago</small>
                </div>
                <p class="mb-1">Please update care notes for patient 22A/UE/BSE/1001</p>
                <small>From: Dr. Ahmed Musa</small>
            </div>
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Shift Schedule Change</h6>
                    <small>5 hours ago</small>
                </div>
                <p class="mb-1">Your schedule for next week has been updated</p>
                <small>From: Nursing Administration</small>
            </div>
        </div>
    `;

    messagesContainer.innerHTML = html;
}

// Utility functions for data generation
function getAssignedPatients(allPatients) {
    const patientList = Object.keys(allPatients).map(matricNumber => ({
        matricNumber,
        name: allPatients[matricNumber].name,
        department: allPatients[matricNumber].department
    }));

    // Assign a subset of patients to this nurse
    const assignedCount = Math.min(Math.floor(patientList.length / 3) + 3, patientList.length);
    return patientList.slice(0, assignedCount);
}

function getPatientStatus() {
    const statuses = [
        { text: 'Stable', class: 'success' },
        { text: 'Monitoring', class: 'warning' },
        { text: 'Critical', class: 'danger' },
        { text: 'Recovering', class: 'info' }
    ];
    return statuses[Math.floor(Math.random() * statuses.length)];
}

function generateRoomNumber() {
    const floor = Math.floor(Math.random() * 3) + 1;
    const room = Math.floor(Math.random() * 20) + 1;
    return `${floor}${room.toString().padStart(2, '0')}`;
}

function getWardInfo() {
    const totalBeds = 20;
    const occupiedBeds = Math.floor(Math.random() * 15) + 8;
    return {
        totalBeds,
        occupiedBeds,
        availableBeds: totalBeds - occupiedBeds,
        occupancyRate: Math.round((occupiedBeds / totalBeds) * 100)
    };
}

function generatePatientForBed() {
    const names = ['John Doe', 'Jane Smith', 'Ahmed Ali', 'Fatima Hassan', 'Michael Brown'];
    return names[Math.floor(Math.random() * names.length)];
}

function generateMedicationSchedule() {
    const medications = [
        { medication: 'Paracetamol', dosage: '500mg' },
        { medication: 'Amoxicillin', dosage: '250mg' },
        { medication: 'Ibuprofen', dosage: '200mg' },
        { medication: 'Metformin', dosage: '500mg' }
    ];

    const schedule = [];
    const times = ['08:00', '12:00', '16:00', '20:00'];
    const patients = ['Patient A', 'Patient B', 'Patient C', 'Patient D'];
    const statuses = ['Due', 'Completed', 'Overdue'];

    for (let i = 0; i < 8; i++) {
        const med = medications[Math.floor(Math.random() * medications.length)];
        schedule.push({
            id: `MED${i + 1}`,
            time: times[Math.floor(Math.random() * times.length)],
            patient: patients[Math.floor(Math.random() * patients.length)],
            medication: med.medication,
            dosage: med.dosage,
            status: statuses[Math.floor(Math.random() * statuses.length)]
        });
    }

    return schedule;
}

function generateVitalSignsSchedule() {
    const vitals = [];
    const patients = ['Patient A', 'Patient B', 'Patient C', 'Patient D', 'Patient E'];

    patients.forEach((patient, index) => {
        vitals.push({
            id: `VIT${index + 1}`,
            patient: patient,
            bp: `${Math.floor(Math.random() * 40) + 110}/${Math.floor(Math.random() * 30) + 70}`,
            pulse: Math.floor(Math.random() * 40) + 60,
            temp: (Math.random() * 2 + 36).toFixed(1),
            resp: Math.floor(Math.random() * 10) + 16,
            o2sat: Math.floor(Math.random() * 5) + 95,
            time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
        });
    });

    return vitals;
}

// Modal functions - Now fully functional
function showRecordVitalsModal() {
    // Set current date and time
    const now = new Date();
    const dateTimeString = now.toISOString().slice(0, 16);
    document.querySelector('#recordVitalsForm input[name="dateTime"]').value = dateTimeString;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('recordVitalsModal'));
    modal.show();
}

function showMedicationModal() {
    // Set current date and time
    const now = new Date();
    const dateTimeString = now.toISOString().slice(0, 16);
    document.querySelector('#medicationForm input[name="adminTime"]').value = dateTimeString;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('medicationModal'));
    modal.show();
}

function showPatientNotesModal() {
    // Set current date and time
    const now = new Date();
    const dateTimeString = now.toISOString().slice(0, 16);
    document.querySelector('#patientNotesForm input[name="noteDateTime"]').value = dateTimeString;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('patientNotesModal'));
    modal.show();
}

function showShiftReportModal() {
    // Set current date
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('#shiftReportForm input[name="shiftDate"]').value = today;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('shiftReportModal'));
    modal.show();
}

function showNurseProfileModal() {
    // Populate profile modal with current nurse data
    if (currentNurse) {
        document.getElementById('profileNurseId').value = currentNurse.id || '';
        document.getElementById('profileName').value = currentNurse.name || '';
        document.getElementById('profileEmail').value = currentNurse.email || '';
        document.getElementById('profilePhone').value = currentNurse.phone || '';
        document.getElementById('profileDepartment').value = currentNurse.department || '';
        document.getElementById('profileShift').value = currentNurse.shift || '';
        document.getElementById('profileLicense').value = currentNurse.licenseNumber || '';
        document.getElementById('profileExperience').value = currentNurse.experience || '';
        document.getElementById('profileEducation').value = currentNurse.education || '';
        document.getElementById('profileSpecialization').value = currentNurse.specialization || '';
    }

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('nurseProfileModal'));
    modal.show();
}

function showNurseSettingsModal() {
    // Populate settings modal with current preferences
    if (currentNurse) {
        document.getElementById('settingsDisplayName').value = currentNurse.name || '';

        // Load saved settings from localStorage
        const savedSettings = JSON.parse(localStorage.getItem('nurseSettings_' + currentNurse.id) || '{}');

        // Account settings
        document.getElementById('emailNotifications').checked = savedSettings.emailNotifications !== false;
        document.getElementById('settingsLanguage').value = savedSettings.language || 'en';

        // Notification settings
        document.getElementById('patientAlerts').checked = savedSettings.patientAlerts !== false;
        document.getElementById('medicationReminders').checked = savedSettings.medicationReminders !== false;
        document.getElementById('shiftUpdates').checked = savedSettings.shiftUpdates !== false;
        document.getElementById('systemMessages').checked = savedSettings.systemMessages !== false;

        // Preferences
        document.getElementById('defaultView').value = savedSettings.defaultView || 'dashboard';
        document.getElementById('themePreference').value = savedSettings.theme || 'light';
        document.getElementById('autoRefresh').checked = savedSettings.autoRefresh !== false;
    }

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('nurseSettingsModal'));
    modal.show();
}

function saveNurseProfile() {
    if (!currentNurse) return;

    // Get updated profile data
    const updatedProfile = {
        ...currentNurse,
        name: document.getElementById('profileName').value,
        email: document.getElementById('profileEmail').value,
        phone: document.getElementById('profilePhone').value,
        education: document.getElementById('profileEducation').value,
        specialization: document.getElementById('profileSpecialization').value,
        lastUpdated: new Date().toISOString()
    };

    // Update current nurse data
    currentNurse = updatedProfile;

    // Update session storage
    sessionStorage.setItem('hospitalUser', JSON.stringify(updatedProfile));
    localStorage.setItem('hospitalUser', JSON.stringify(updatedProfile));

    // Update nurse accounts in the system
    if (window.nurseAccounts && window.nurseAccounts[currentNurse.id]) {
        window.nurseAccounts[currentNurse.id] = updatedProfile;
    }

    // Update UI
    updateNurseInfo();

    // Close modal and show success message
    const modal = bootstrap.Modal.getInstance(document.getElementById('nurseProfileModal'));
    modal.hide();

    showSuccessAlert('Profile updated successfully!');
}

function saveNurseSettings() {
    if (!currentNurse) return;

    // Collect all settings
    const settings = {
        // Account settings
        displayName: document.getElementById('settingsDisplayName').value,
        emailNotifications: document.getElementById('emailNotifications').checked,
        language: document.getElementById('settingsLanguage').value,

        // Notification settings
        patientAlerts: document.getElementById('patientAlerts').checked,
        medicationReminders: document.getElementById('medicationReminders').checked,
        shiftUpdates: document.getElementById('shiftUpdates').checked,
        systemMessages: document.getElementById('systemMessages').checked,

        // Preferences
        defaultView: document.getElementById('defaultView').value,
        theme: document.getElementById('themePreference').value,
        autoRefresh: document.getElementById('autoRefresh').checked,

        lastUpdated: new Date().toISOString()
    };

    // Save settings to localStorage
    localStorage.setItem('nurseSettings_' + currentNurse.id, JSON.stringify(settings));

    // Apply theme if changed
    applyTheme(settings.theme);

    // Close modal and show success message
    const modal = bootstrap.Modal.getInstance(document.getElementById('nurseSettingsModal'));
    modal.hide();

    showSuccessAlert('Settings saved successfully!');
}

function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Validate inputs
    if (!currentPassword || !newPassword || !confirmPassword) {
        showErrorAlert('Please fill in all password fields.');
        return;
    }

    if (newPassword !== confirmPassword) {
        showErrorAlert('New passwords do not match.');
        return;
    }

    if (newPassword.length < 6) {
        showErrorAlert('New password must be at least 6 characters long.');
        return;
    }

    // In a real application, you would validate the current password against the server
    // For this demo, we'll just show a success message
    showSuccessAlert('Password changed successfully! Please login again with your new password.');

    // Clear password fields
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
}

function applyTheme(theme) {
    const body = document.body;

    // Remove existing theme classes
    body.classList.remove('theme-light', 'theme-dark');

    if (theme === 'dark') {
        body.classList.add('theme-dark');
    } else if (theme === 'light') {
        body.classList.add('theme-light');
    } else if (theme === 'auto') {
        // Use system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('theme-dark');
        } else {
            body.classList.add('theme-light');
        }
    }
}

// Action functions
function viewPatientDetails(matricNumber) { showSuccessAlert(`Viewing details for ${matricNumber}`); }
function recordVitalsFor(matricNumber) { showSuccessAlert(`Recording vitals for ${matricNumber}`); }
function giveMedicationTo(matricNumber) { showSuccessAlert(`Giving medication to ${matricNumber}`); }
function updateNotesFor(matricNumber) { showSuccessAlert(`Updating notes for ${matricNumber}`); }
function addPatientCare() { showSuccessAlert('Add patient care feature coming soon'); }
function viewBedDetails(bedNumber) { showSuccessAlert(`Viewing bed ${bedNumber} details`); }
function assignBed(bedNumber) { showSuccessAlert(`Assigning bed ${bedNumber}`); }
function administerMed(medId) { showSuccessAlert(`Administering medication ${medId}`); }
function viewMedDetails(medId) { showSuccessAlert(`Viewing medication details ${medId}`); }
function updateVitals(vitalId) { showSuccessAlert(`Updating vitals ${vitalId}`); }
function viewVitalHistory(vitalId) { showSuccessAlert(`Viewing vital history ${vitalId}`); }
function composeMessage() { showSuccessAlert('Compose message feature coming soon'); }

// Save functions for quick action modals
function saveVitalSigns() {
    const form = document.getElementById('recordVitalsForm');
    const formData = new FormData(form);

    // Validate required fields
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Create vital signs record
    const vitalSigns = {
        id: 'VS' + Date.now(),
        matricNumber: formData.get('matricNumber'),
        dateTime: formData.get('dateTime'),
        bloodPressure: formData.get('bloodPressure'),
        heartRate: formData.get('heartRate'),
        temperature: formData.get('temperature'),
        respiratoryRate: formData.get('respiratoryRate'),
        oxygenSaturation: formData.get('oxygenSaturation'),
        weight: formData.get('weight'),
        notes: formData.get('notes'),
        recordedBy: currentNurse.name,
        nurseId: currentNurse.id,
        timestamp: new Date().toISOString()
    };

    // Save to localStorage
    const vitalRecords = JSON.parse(localStorage.getItem('vitalSignsRecords') || '[]');
    vitalRecords.push(vitalSigns);
    localStorage.setItem('vitalSignsRecords', JSON.stringify(vitalRecords));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('recordVitalsModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Vital signs recorded successfully for patient ${vitalSigns.matricNumber}!`);
}

function saveMedicationAdministration() {
    const form = document.getElementById('medicationForm');
    const formData = new FormData(form);

    // Validate required fields
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Create medication record
    const medicationRecord = {
        id: 'MED' + Date.now(),
        matricNumber: formData.get('matricNumber'),
        adminTime: formData.get('adminTime'),
        medicationName: formData.get('medicationName'),
        dosage: formData.get('dosage'),
        route: formData.get('route'),
        frequency: formData.get('frequency'),
        instructions: formData.get('instructions'),
        administeredBy: currentNurse.name,
        nurseId: currentNurse.id,
        timestamp: new Date().toISOString()
    };

    // Save to localStorage
    const medicationRecords = JSON.parse(localStorage.getItem('medicationRecords') || '[]');
    medicationRecords.push(medicationRecord);
    localStorage.setItem('medicationRecords', JSON.stringify(medicationRecords));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('medicationModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Medication ${medicationRecord.medicationName} administered successfully to patient ${medicationRecord.matricNumber}!`);
}

function savePatientNotes() {
    const form = document.getElementById('patientNotesForm');
    const formData = new FormData(form);

    // Validate required fields
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Create patient note record
    const patientNote = {
        id: 'NOTE' + Date.now(),
        matricNumber: formData.get('matricNumber'),
        noteDateTime: formData.get('noteDateTime'),
        noteType: formData.get('noteType'),
        condition: formData.get('condition'),
        notes: formData.get('notes'),
        followUp: formData.get('followUp'),
        createdBy: currentNurse.name,
        nurseId: currentNurse.id,
        timestamp: new Date().toISOString()
    };

    // Save to localStorage
    const patientNotes = JSON.parse(localStorage.getItem('patientNotes') || '[]');
    patientNotes.push(patientNote);
    localStorage.setItem('patientNotes', JSON.stringify(patientNotes));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('patientNotesModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Patient notes updated successfully for patient ${patientNote.matricNumber}!`);
}

function saveShiftReport() {
    const form = document.getElementById('shiftReportForm');
    const formData = new FormData(form);

    // Validate required fields
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Create shift report record
    const shiftReport = {
        id: 'SHIFT' + Date.now(),
        shiftDate: formData.get('shiftDate'),
        shiftType: formData.get('shiftType'),
        patientsAssigned: formData.get('patientsAssigned'),
        medicationsGiven: formData.get('medicationsGiven'),
        shiftSummary: formData.get('shiftSummary'),
        patientUpdates: formData.get('patientUpdates'),
        issues: formData.get('issues'),
        handoverNotes: formData.get('handoverNotes'),
        createdBy: currentNurse.name,
        nurseId: currentNurse.id,
        department: currentNurse.department,
        timestamp: new Date().toISOString()
    };

    // Save to localStorage
    const shiftReports = JSON.parse(localStorage.getItem('shiftReports') || '[]');
    shiftReports.push(shiftReport);
    localStorage.setItem('shiftReports', JSON.stringify(shiftReports));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('shiftReportModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Shift report created successfully for ${shiftReport.shiftDate} - ${shiftReport.shiftType}!`);
}

// Make profile and settings functions globally available
window.saveNurseProfile = saveNurseProfile;
window.saveNurseSettings = saveNurseSettings;
window.changePassword = changePassword;
window.saveVitalSigns = saveVitalSigns;
window.saveMedicationAdministration = saveMedicationAdministration;
window.savePatientNotes = savePatientNotes;
window.saveShiftReport = saveShiftReport;
