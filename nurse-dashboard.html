<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nurse Dashboard - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-info fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="index-redesigned.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital - Nurse Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="nurseDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-nurse me-2"></i>
                        <span id="nurseName">Nurse</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="fas fa-user me-2"></i>My Profile
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-5 pt-3">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar bg-light">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column sidebar-nav">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showDashboard()">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPatients()">
                                <i class="fas fa-users me-2"></i>My Patients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showWardManagement()">
                                <i class="fas fa-bed me-2"></i>Ward Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showMedications()">
                                <i class="fas fa-pills me-2"></i>Medications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showVitalSigns()">
                                <i class="fas fa-heartbeat me-2"></i>Vital Signs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showShiftReports()">
                                <i class="fas fa-clipboard-list me-2"></i>Shift Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSchedule()">
                                <i class="fas fa-calendar-alt me-2"></i>My Schedule
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showMessages()">
                                <i class="fas fa-envelope me-2"></i>Messages
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ms-sm-auto px-md-4">
                <!-- Dashboard Content -->
                <div id="dashboardContent" class="content-section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2 fw-bold text-dark">
                            <i class="fas fa-tachometer-alt me-2 text-info"></i>
                            Nurse Dashboard
                        </h1>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="currentDate"></span>
                        </div>
                    </div>

                    <!-- Welcome Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-gradient-info text-white">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h4 class="mb-2">Welcome back, <span id="welcomeName">Nurse</span>!</h4>
                                            <p class="mb-0 opacity-90">
                                                <i class="fas fa-hospital me-2"></i>
                                                Department: <span id="nurseDepartment">General Ward</span> | 
                                                <i class="fas fa-clock me-2 ms-3"></i>
                                                Shift: <span id="nurseShift">Day Shift</span>
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="nurse-avatar">
                                                <img id="dashboardAvatar" src="https://cdn-icons-png.flaticon.com/128/2785/2785482.png" alt="Nurse Avatar" class="rounded-circle bg-white p-2 profile-picture" style="width: 80px; height: 80px; object-fit: cover;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row g-4 mb-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="totalPatients">12</h3>
                                    <p class="mb-0">Assigned Patients</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-pills fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="medicationsDue">8</h3>
                                    <p class="mb-0">Medications Due</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-heartbeat fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="vitalsNeeded">5</h3>
                                    <p class="mb-0">Vitals Needed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clipboard-check fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="tasksCompleted">15</h3>
                                    <p class="mb-0">Tasks Completed</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-bolt me-2 text-warning"></i>Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <button class="btn btn-outline-primary w-100" onclick="recordVitals()">
                                                <i class="fas fa-heartbeat me-2"></i>Record Vitals
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-success w-100" onclick="administerMedication()">
                                                <i class="fas fa-pills me-2"></i>Give Medication
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-info w-100" onclick="updatePatientNotes()">
                                                <i class="fas fa-notes-medical me-2"></i>Update Notes
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-warning w-100" onclick="createShiftReport()">
                                                <i class="fas fa-clipboard-list me-2"></i>Shift Report
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-clock me-2 text-info"></i>Today's Schedule</h5>
                                </div>
                                <div class="card-body">
                                    <div class="schedule-item d-flex justify-content-between align-items-center mb-2">
                                        <span><i class="fas fa-circle text-success me-2"></i>08:00 - Shift Handover</span>
                                        <span class="badge bg-success">Completed</span>
                                    </div>
                                    <div class="schedule-item d-flex justify-content-between align-items-center mb-2">
                                        <span><i class="fas fa-circle text-warning me-2"></i>09:00 - Morning Rounds</span>
                                        <span class="badge bg-warning">In Progress</span>
                                    </div>
                                    <div class="schedule-item d-flex justify-content-between align-items-center mb-2">
                                        <span><i class="fas fa-circle text-secondary me-2"></i>11:00 - Medication Round</span>
                                        <span class="badge bg-secondary">Pending</span>
                                    </div>
                                    <div class="schedule-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-circle text-secondary me-2"></i>14:00 - Vital Signs Check</span>
                                        <span class="badge bg-secondary">Pending</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-history me-2 text-secondary"></i>Recent Activities</h5>
                                </div>
                                <div class="card-body">
                                    <div class="activity-item d-flex align-items-center mb-3">
                                        <div class="activity-icon bg-success text-white rounded-circle me-3">
                                            <i class="fas fa-pills"></i>
                                        </div>
                                        <div>
                                            <strong>Medication administered</strong> to Patient 22A/UE/BSE/1001
                                            <br><small class="text-muted">10 minutes ago</small>
                                        </div>
                                    </div>
                                    <div class="activity-item d-flex align-items-center mb-3">
                                        <div class="activity-icon bg-info text-white rounded-circle me-3">
                                            <i class="fas fa-heartbeat"></i>
                                        </div>
                                        <div>
                                            <strong>Vital signs recorded</strong> for Patient 22B/UE/BNS/1002
                                            <br><small class="text-muted">25 minutes ago</small>
                                        </div>
                                    </div>
                                    <div class="activity-item d-flex align-items-center">
                                        <div class="activity-icon bg-warning text-white rounded-circle me-3">
                                            <i class="fas fa-notes-medical"></i>
                                        </div>
                                        <div>
                                            <strong>Patient notes updated</strong> for Patient 22A/UE/LLB/1003
                                            <br><small class="text-muted">1 hour ago</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other content sections will be loaded dynamically -->
                <div id="patientsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading patients...</p>
                    </div>
                </div>

                <div id="wardManagementContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading ward management...</p>
                    </div>
                </div>

                <div id="medicationsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading medications...</p>
                    </div>
                </div>

                <div id="vitalSignsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading vital signs...</p>
                    </div>
                </div>

                <div id="shiftReportsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading shift reports...</p>
                    </div>
                </div>

                <div id="scheduleContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-secondary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading schedule...</p>
                    </div>
                </div>

                <div id="messagesContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-dark" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading messages...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nurse Profile Modal -->
    <div class="modal fade" id="nurseProfileModal" tabindex="-1" aria-labelledby="nurseProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="nurseProfileModalLabel">
                        <i class="fas fa-user-nurse me-2"></i>My Profile
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="profile-avatar mb-3">
                                <img id="profileAvatar" src="https://cdn-icons-png.flaticon.com/128/2785/2785482.png" alt="Nurse Avatar" class="rounded-circle border border-info profile-picture" style="width: 120px; height: 120px; object-fit: cover;">
                            </div>
                            <button class="btn btn-outline-info btn-sm" onclick="profilePictureManager.showProfilePictureModal()">
                                <i class="fas fa-camera me-2"></i>Change Photo
                            </button>
                        </div>
                        <div class="col-md-8">
                            <form id="nurseProfileForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Nurse ID</label>
                                        <input type="text" class="form-control" id="profileNurseId" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="profileName">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" id="profileEmail">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="profilePhone">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Department</label>
                                        <input type="text" class="form-control" id="profileDepartment" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Shift</label>
                                        <input type="text" class="form-control" id="profileShift" readonly>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">License Number</label>
                                        <input type="text" class="form-control" id="profileLicense" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Experience</label>
                                        <input type="text" class="form-control" id="profileExperience" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Education</label>
                                    <input type="text" class="form-control" id="profileEducation">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Specialization</label>
                                    <input type="text" class="form-control" id="profileSpecialization">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-info" onclick="saveNurseProfile()">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Nurse Settings Modal -->
    <div class="modal fade" id="nurseSettingsModal" tabindex="-1" aria-labelledby="nurseSettingsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title" id="nurseSettingsModalLabel">
                        <i class="fas fa-cog me-2"></i>Settings
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="nav flex-column nav-pills" id="settings-tab" role="tablist" aria-orientation="vertical">
                                <button class="nav-link active" id="account-tab" data-bs-toggle="pill" data-bs-target="#account" type="button" role="tab">
                                    <i class="fas fa-user me-2"></i>Account
                                </button>
                                <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                                    <i class="fas fa-shield-alt me-2"></i>Security
                                </button>
                                <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
                                    <i class="fas fa-bell me-2"></i>Notifications
                                </button>
                                <button class="nav-link" id="preferences-tab" data-bs-toggle="pill" data-bs-target="#preferences" type="button" role="tab">
                                    <i class="fas fa-sliders-h me-2"></i>Preferences
                                </button>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="tab-content" id="settings-tabContent">
                                <!-- Account Settings -->
                                <div class="tab-pane fade show active" id="account" role="tabpanel">
                                    <h6 class="mb-3">Account Settings</h6>
                                    <form id="accountSettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">Display Name</label>
                                            <input type="text" class="form-control" id="settingsDisplayName">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email Notifications</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                                <label class="form-check-label" for="emailNotifications">
                                                    Receive email notifications
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Language</label>
                                            <select class="form-select" id="settingsLanguage">
                                                <option value="en">English</option>
                                                <option value="ha">Hausa</option>
                                                <option value="yo">Yoruba</option>
                                                <option value="ig">Igbo</option>
                                            </select>
                                        </div>
                                    </form>
                                </div>

                                <!-- Security Settings -->
                                <div class="tab-pane fade" id="security" role="tabpanel">
                                    <h6 class="mb-3">Security Settings</h6>
                                    <form id="securitySettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">Current Password</label>
                                            <input type="password" class="form-control" id="currentPassword">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">New Password</label>
                                            <input type="password" class="form-control" id="newPassword">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Confirm New Password</label>
                                            <input type="password" class="form-control" id="confirmPassword">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                                <label class="form-check-label" for="twoFactorAuth">
                                                    Enable Two-Factor Authentication
                                                </label>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-warning" onclick="changePassword()">
                                            <i class="fas fa-key me-2"></i>Change Password
                                        </button>
                                    </form>
                                </div>

                                <!-- Notification Settings -->
                                <div class="tab-pane fade" id="notifications" role="tabpanel">
                                    <h6 class="mb-3">Notification Preferences</h6>
                                    <form id="notificationSettingsForm">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="patientAlerts" checked>
                                                <label class="form-check-label" for="patientAlerts">
                                                    Patient care alerts
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="medicationReminders" checked>
                                                <label class="form-check-label" for="medicationReminders">
                                                    Medication reminders
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="shiftUpdates" checked>
                                                <label class="form-check-label" for="shiftUpdates">
                                                    Shift updates
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="systemMessages">
                                                <label class="form-check-label" for="systemMessages">
                                                    System messages
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- Preferences -->
                                <div class="tab-pane fade" id="preferences" role="tabpanel">
                                    <h6 class="mb-3">Dashboard Preferences</h6>
                                    <form id="preferencesForm">
                                        <div class="mb-3">
                                            <label class="form-label">Default Dashboard View</label>
                                            <select class="form-select" id="defaultView">
                                                <option value="dashboard">Dashboard Overview</option>
                                                <option value="patients">My Patients</option>
                                                <option value="schedule">My Schedule</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Theme</label>
                                            <select class="form-select" id="themePreference">
                                                <option value="light">Light Theme</option>
                                                <option value="dark">Dark Theme</option>
                                                <option value="auto">Auto (System)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                                <label class="form-check-label" for="autoRefresh">
                                                    Auto-refresh dashboard data
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveNurseSettings()">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Action Modals -->

    <!-- Record Vitals Modal -->
    <div class="modal fade" id="recordVitalsModal" tabindex="-1" aria-labelledby="recordVitalsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="recordVitalsModalLabel">
                        <i class="fas fa-heartbeat me-2"></i>Record Vital Signs
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="recordVitalsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Patient Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required placeholder="e.g., 22A/UE/BNS/1001">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Date & Time</label>
                                <input type="datetime-local" class="form-control" name="dateTime" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Blood Pressure (mmHg)</label>
                                <input type="text" class="form-control" name="bloodPressure" placeholder="120/80" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Heart Rate (bpm)</label>
                                <input type="number" class="form-control" name="heartRate" placeholder="72" min="40" max="200" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Temperature (°C)</label>
                                <input type="number" class="form-control" name="temperature" placeholder="36.5" step="0.1" min="30" max="45" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Respiratory Rate (breaths/min)</label>
                                <input type="number" class="form-control" name="respiratoryRate" placeholder="16" min="8" max="40" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Oxygen Saturation (%)</label>
                                <input type="number" class="form-control" name="oxygenSaturation" placeholder="98" min="70" max="100" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Weight (kg)</label>
                                <input type="number" class="form-control" name="weight" placeholder="70" min="1" max="300">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Additional observations or notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveVitalSigns()">
                        <i class="fas fa-save me-2"></i>Record Vitals
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Administer Medication Modal -->
    <div class="modal fade" id="medicationModal" tabindex="-1" aria-labelledby="medicationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="medicationModalLabel">
                        <i class="fas fa-pills me-2"></i>Administer Medication
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="medicationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Patient Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required placeholder="e.g., 22A/UE/BNS/1001">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Administration Time</label>
                                <input type="datetime-local" class="form-control" name="adminTime" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Medication Name</label>
                                <select class="form-select" name="medicationName" required>
                                    <option value="">Select Medication</option>
                                    <option value="Paracetamol 500mg">Paracetamol 500mg</option>
                                    <option value="Amoxicillin 500mg">Amoxicillin 500mg</option>
                                    <option value="Ibuprofen 400mg">Ibuprofen 400mg</option>
                                    <option value="Insulin 100IU/ml">Insulin 100IU/ml</option>
                                    <option value="Metformin 500mg">Metformin 500mg</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Dosage</label>
                                <input type="text" class="form-control" name="dosage" required placeholder="e.g., 2 tablets, 5ml">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Route of Administration</label>
                                <select class="form-select" name="route" required>
                                    <option value="">Select Route</option>
                                    <option value="Oral">Oral</option>
                                    <option value="Intravenous">Intravenous (IV)</option>
                                    <option value="Intramuscular">Intramuscular (IM)</option>
                                    <option value="Subcutaneous">Subcutaneous</option>
                                    <option value="Topical">Topical</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Frequency</label>
                                <select class="form-select" name="frequency" required>
                                    <option value="">Select Frequency</option>
                                    <option value="Once daily">Once daily</option>
                                    <option value="Twice daily">Twice daily</option>
                                    <option value="Three times daily">Three times daily</option>
                                    <option value="Four times daily">Four times daily</option>
                                    <option value="As needed">As needed (PRN)</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Instructions</label>
                            <textarea class="form-control" name="instructions" rows="3" placeholder="Special instructions for administration..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveMedicationAdministration()">
                        <i class="fas fa-pills me-2"></i>Administer Medication
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Patient Notes Modal -->
    <div class="modal fade" id="patientNotesModal" tabindex="-1" aria-labelledby="patientNotesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="patientNotesModalLabel">
                        <i class="fas fa-notes-medical me-2"></i>Update Patient Notes
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="patientNotesForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Patient Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required placeholder="e.g., 22A/UE/BNS/1001">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Note Date & Time</label>
                                <input type="datetime-local" class="form-control" name="noteDateTime" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Note Type</label>
                            <select class="form-select" name="noteType" required>
                                <option value="">Select Note Type</option>
                                <option value="Assessment">Assessment</option>
                                <option value="Care Plan">Care Plan</option>
                                <option value="Progress Note">Progress Note</option>
                                <option value="Discharge Planning">Discharge Planning</option>
                                <option value="Incident Report">Incident Report</option>
                                <option value="General Observation">General Observation</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Patient Condition</label>
                            <select class="form-select" name="condition">
                                <option value="">Select Condition</option>
                                <option value="Stable">Stable</option>
                                <option value="Improving">Improving</option>
                                <option value="Deteriorating">Deteriorating</option>
                                <option value="Critical">Critical</option>
                                <option value="Recovering">Recovering</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nursing Notes</label>
                            <textarea class="form-control" name="notes" rows="6" required placeholder="Enter detailed nursing notes, observations, and care provided..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Follow-up Actions</label>
                            <textarea class="form-control" name="followUp" rows="3" placeholder="Any follow-up actions required..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-info" onclick="savePatientNotes()">
                        <i class="fas fa-save me-2"></i>Save Notes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Shift Report Modal -->
    <div class="modal fade" id="shiftReportModal" tabindex="-1" aria-labelledby="shiftReportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="shiftReportModalLabel">
                        <i class="fas fa-clipboard-list me-2"></i>Create Shift Report
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="shiftReportForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Shift Date</label>
                                <input type="date" class="form-control" name="shiftDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Shift Type</label>
                                <select class="form-select" name="shiftType" required>
                                    <option value="">Select Shift</option>
                                    <option value="Day Shift">Day Shift (8:00 AM - 4:00 PM)</option>
                                    <option value="Evening Shift">Evening Shift (4:00 PM - 12:00 AM)</option>
                                    <option value="Night Shift">Night Shift (12:00 AM - 8:00 AM)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Patients Assigned</label>
                                <input type="number" class="form-control" name="patientsAssigned" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Medications Administered</label>
                                <input type="number" class="form-control" name="medicationsGiven" min="0" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Shift Summary</label>
                            <textarea class="form-control" name="shiftSummary" rows="4" required placeholder="Overall summary of the shift activities..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Patient Updates</label>
                            <textarea class="form-control" name="patientUpdates" rows="4" placeholder="Important patient updates and changes in condition..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Issues/Concerns</label>
                            <textarea class="form-control" name="issues" rows="3" placeholder="Any issues, concerns, or incidents during the shift..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Handover Notes</label>
                            <textarea class="form-control" name="handoverNotes" rows="3" placeholder="Important information for the next shift..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-warning text-dark" onclick="saveShiftReport()">
                        <i class="fas fa-save me-2"></i>Save Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/nurse-login.js"></script>
    <script src="js/profile-picture-manager.js"></script>
    <script src="js/nurse-dashboard.js"></script>
</body>
</html>
