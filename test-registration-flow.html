<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration Flow - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0"><i class="fas fa-user-plus me-2"></i>Patient Registration & Login Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Test Instructions</h5>
                            <p class="mb-2">Follow these steps to test the complete registration and login flow:</p>
                            <ol class="mb-0">
                                <li><strong>Register a new patient</strong> using the registration form</li>
                                <li><strong>Note your Patient ID</strong> from the success modal</li>
                                <li><strong>Login</strong> using your new credentials</li>
                                <li><strong>Access your dashboard</strong> and see your details</li>
                            </ol>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-user-plus me-2 text-success"></i>Step 1: Register New Patient</h5>
                                <p class="text-muted">Create a new patient account with your details.</p>
                                <a href="patient-registration.html" class="btn btn-success btn-lg w-100 mb-3">
                                    <i class="fas fa-user-plus me-2"></i>Start Registration
                                </a>
                                
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>Sample Test Data:</h6>
                                        <small class="text-muted">
                                            <strong>Name:</strong> Test User<br>
                                            <strong>Email:</strong> <EMAIL><br>
                                            <strong>Phone:</strong> +234-************<br>
                                            <strong>Password:</strong> test123456<br>
                                            <em>Fill other fields as needed</em>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-sign-in-alt me-2 text-primary"></i>Step 2: Login with New Account</h5>
                                <p class="text-muted">Use your new Patient ID and password to login.</p>
                                <a href="patient-login.html" class="btn btn-primary btn-lg w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                                </a>
                                
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6>After Registration:</h6>
                                        <small class="text-muted">
                                            <strong>Patient ID:</strong> Will be generated (e.g., PT009)<br>
                                            <strong>Password:</strong> What you entered during registration<br>
                                            <em>Save your Patient ID from the success modal!</em>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-list me-2 text-info"></i>Existing Test Accounts</h5>
                                <p class="text-muted">You can also test with these pre-existing accounts:</p>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Patient ID</th>
                                                <th>Password</th>
                                                <th>Name</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>PT001</code></td>
                                                <td><code>patient123</code></td>
                                                <td>John Doe</td>
                                            </tr>
                                            <tr>
                                                <td><code>PT002</code></td>
                                                <td><code>patient456</code></td>
                                                <td>Jane Smith</td>
                                            </tr>
                                            <tr>
                                                <td><code>PT003</code></td>
                                                <td><code>patient789</code></td>
                                                <td>Michael Johnson</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-tools me-2 text-warning"></i>Debug Tools</h5>
                                <p class="text-muted">Tools to help test and debug the system:</p>
                                
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="showRegisteredPatients()">
                                    <i class="fas fa-users me-2"></i>View All Registered Patients
                                </button>
                                
                                <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="clearRegisteredPatients()">
                                    <i class="fas fa-trash me-2"></i>Clear Registered Patients
                                </button>
                                
                                <button class="btn btn-outline-secondary btn-sm w-100" onclick="exportPatientData()">
                                    <i class="fas fa-download me-2"></i>Export Patient Data
                                </button>
                                
                                <div id="debugOutput" class="mt-3"></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-4">
                            <h6><i class="fas fa-check-circle me-2"></i>What to Expect:</h6>
                            <ul class="mb-0">
                                <li><strong>Registration:</strong> Form validation, unique email check, Patient ID generation</li>
                                <li><strong>Success Modal:</strong> Shows your new Patient ID and login instructions</li>
                                <li><strong>Login:</strong> Works with both old and new patient accounts</li>
                                <li><strong>Dashboard:</strong> Displays your personal information from registration</li>
                                <li><strong>Data Persistence:</strong> Your account is saved in browser storage</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showRegisteredPatients() {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const output = document.getElementById('debugOutput');
            
            if (Object.keys(registeredPatients).length === 0) {
                output.innerHTML = '<div class="alert alert-info">No registered patients found.</div>';
                return;
            }
            
            let html = '<div class="alert alert-info"><h6>Registered Patients:</h6><ul class="mb-0">';
            Object.keys(registeredPatients).forEach(id => {
                const patient = registeredPatients[id];
                html += `<li><strong>${id}:</strong> ${patient.name} (${patient.email})</li>`;
            });
            html += '</ul></div>';
            
            output.innerHTML = html;
        }
        
        function clearRegisteredPatients() {
            if (confirm('Are you sure you want to clear all registered patients? This cannot be undone.')) {
                localStorage.removeItem('registeredPatients');
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-warning">All registered patients have been cleared.</div>';
            }
        }
        
        function exportPatientData() {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const dataStr = JSON.stringify(registeredPatients, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'registered-patients.json';
            link.click();
            
            document.getElementById('debugOutput').innerHTML = '<div class="alert alert-success">Patient data exported successfully!</div>';
        }
    </script>
</body>
</html>
