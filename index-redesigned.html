<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newgate Hospital - Modern Healthcare Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007BFF;
            --secondary-color: #6C757D;
            --success-color: #28A745;
            --warning-color: #FFC107;
            --danger-color: #DC3545;
            --info-color: #17A2B8;
            --light-bg: #F8F9FA;
            --dark-text: #343A40;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark-text);
        }

        /* Modern Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)), url('IB.webp');
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            background-attachment: scroll; /* Changed from fixed for better quality */
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            /* Advanced image quality optimizations */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: high-quality;
            image-rendering: auto;
            /* Hardware acceleration for crisp rendering */
            -webkit-backface-visibility: hidden;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            /* Force GPU acceleration */
            will-change: transform;
            /* Smooth rendering */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Alternative high-quality background for modern browsers */
        @supports (background-image: url('IB.webp')) {
            .hero-section {
                background-image: linear-gradient(rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6)), url('IB.webp');
                background-blend-mode: normal;
            }
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        /* Additional overlay for better text contrast */
        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
            z-index: 2;
        }

        .hero-content {
            position: relative;
            z-index: 10;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* Modern Cards */
        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        /* Action Buttons */
        .action-btn {
            background: white;
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            text-decoration: none;
            color: var(--dark-text);
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: block;
            text-align: center;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            color: var(--dark-text);
        }

        .action-btn i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        /* Portal Cards */
        .portal-card {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-radius: 20px;
            padding: 2rem;
            color: white;
            text-decoration: none;
            display: block;
            transition: all 0.3s ease;
            border: none;
        }

        .portal-card:hover {
            transform: scale(1.05);
            color: white;
        }

        /* Statistics Section */
        .stats-section {
            background: var(--light-bg);
            padding: 5rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--secondary-color);
            margin-top: 0.5rem;
        }

        /* Contact Section */
        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5rem 0;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .feature-card {
                margin-bottom: 2rem;
            }
        }

        /* Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in:nth-child(1) { animation-delay: 0.1s; }
        .fade-in:nth-child(2) { animation-delay: 0.2s; }
        .fade-in:nth-child(3) { animation-delay: 0.3s; }
        .fade-in:nth-child(4) { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-hospital me-2"></i>Newgate Hospital
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0">
                            <li><h6 class="dropdown-header">Choose Your Portal</h6></li>
                            <li><a class="dropdown-item py-2" href="patient-login.html">
                                <i class="fas fa-user text-primary me-3"></i>Student Portal
                            </a></li>
                            <li><a class="dropdown-item py-2" href="doctor-login.html">
                                <i class="fas fa-user-md text-success me-3"></i>Doctor Portal
                            </a></li>
                            <li><a class="dropdown-item py-2" href="nurse-login.html">
                                <i class="fas fa-user-nurse text-info me-3"></i>Nurse Portal
                            </a></li>
                            <li><a class="dropdown-item py-2" href="pharmacy-login.html">
                                <i class="fas fa-pills text-warning me-3"></i>Pharmacy Portal
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item py-2" href="admin-login.html">
                                <i class="fas fa-user-shield text-danger me-3"></i>Admin Portal
                            </a></li>
                        </ul>
                    </div>
                    <a href="patient-registration.html" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>Register
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content text-white">
                        <h1 class="hero-title fade-in">
                            Modern Healthcare<br>
                            <span style="color: #FFC107;">Management</span>
                        </h1>
                        <p class="hero-subtitle fade-in">
                            Experience the future of university healthcare with our comprehensive digital platform. 
                            Seamless appointments, instant access to medical records, and 24/7 health monitoring.
                        </p>
                        
                        <div class="row g-3 fade-in">
                            <div class="col-md-6">
                                <a href="patient-registration.html" class="action-btn">
                                    <i class="fas fa-user-plus" style="color: var(--success-color);"></i>
                                    <strong>Get Started</strong>
                                    <small class="d-block text-muted">Register as new student</small>
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="patient-login.html" class="action-btn">
                                    <i class="fas fa-sign-in-alt" style="color: var(--primary-color);"></i>
                                    <strong>Student Login</strong>
                                    <small class="d-block text-muted">Access your account</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="row g-4 fade-in">
                        <div class="col-md-6">
                            <a href="appointment.html" class="portal-card">
                                <i class="fas fa-calendar-check fa-3x mb-3"></i>
                                <h5>Book Appointment</h5>
                                <p class="mb-0">Schedule your medical consultation</p>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="patient-dashboard.html" class="portal-card" style="background: linear-gradient(135deg, var(--success-color), #1e7e34);">
                                <i class="fas fa-file-medical fa-3x mb-3"></i>
                                <h5>Health Records</h5>
                                <p class="mb-0">View your medical history</p>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="nurse-dashboard.html" class="portal-card" style="background: linear-gradient(135deg, var(--info-color), #117a8b);">
                                <i class="fas fa-user-nurse fa-3x mb-3"></i>
                                <h5>Nursing Care</h5>
                                <p class="mb-0">Professional nursing services</p>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="pharmacy-dashboard.html" class="portal-card" style="background: linear-gradient(135deg, var(--warning-color), #d39e00);">
                                <i class="fas fa-pills fa-3x mb-3"></i>
                                <h5>Pharmacy</h5>
                                <p class="mb-0">Medicine & prescriptions</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4">Comprehensive Healthcare Services</h2>
                    <p class="lead text-muted">Everything you need for your health and wellness journey</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-primary text-white">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Medical Consultations</h4>
                        <p class="text-muted">Expert medical consultations with qualified doctors and specialists available 24/7.</p>
                        <div class="mt-3">
                            <a href="doctor-login.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-user-md me-2"></i>Access Doctor Portal
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-success text-white">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Health Monitoring</h4>
                        <p class="text-muted">Continuous health monitoring and vital signs tracking for optimal wellness.</p>
                        <div class="mt-3">
                            <a href="patient-login.html" class="btn btn-success btn-sm">
                                <i class="fas fa-heartbeat me-2"></i>View Health Records
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-warning text-white">
                            <i class="fas fa-pills"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Pharmacy Services</h4>
                        <p class="text-muted">Complete pharmacy services with prescription management and medicine delivery.</p>
                        <div class="mt-3">
                            <a href="pharmacy-login.html" class="btn btn-warning btn-sm">
                                <i class="fas fa-pills me-2"></i>Access Pharmacy Portal
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-info text-white">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Easy Scheduling</h4>
                        <p class="text-muted">Simple online appointment booking system with instant confirmation.</p>
                        <div class="mt-3">
                            <a href="appointment.html" class="btn btn-info btn-sm">
                                <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-danger text-white">
                            <i class="fas fa-ambulance"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Emergency Care</h4>
                        <p class="text-muted">24/7 emergency medical services with rapid response capabilities.</p>
                        <div class="mt-3">
                            <button class="btn btn-danger btn-sm" onclick="showEmergencyInfo()">
                                <i class="fas fa-phone me-2"></i>Emergency Contact
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card fade-in">
                        <div class="feature-icon bg-secondary text-white">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Secure Records</h4>
                        <p class="text-muted">HIPAA-compliant secure storage and management of all medical records.</p>
                        <div class="mt-3">
                            <a href="patient-login.html" class="btn btn-secondary btn-sm">
                                <i class="fas fa-file-medical me-2"></i>Access Records
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item fade-in">
                        <span class="stat-number">5,000+</span>
                        <div class="stat-label">Students Served</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item fade-in">
                        <span class="stat-number">50+</span>
                        <div class="stat-label">Medical Professionals</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item fade-in">
                        <span class="stat-number">24/7</span>
                        <div class="stat-label">Emergency Services</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item fade-in">
                        <span class="stat-number">99.9%</span>
                        <div class="stat-label">System Uptime</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="fade-in">
                        <h2 class="display-5 fw-bold mb-4">About Newgate Hospital</h2>
                        <p class="lead mb-4">
                            Leading the future of university healthcare with innovative technology and compassionate care.
                        </p>
                        <p class="text-muted mb-4">
                            Located on Minna-Bida Road in Minna, Niger State, Newgate Hospital has been serving the university
                            community with excellence for years. Our state-of-the-art facility combines modern medical technology
                            with personalized care to ensure the best health outcomes for our students.
                        </p>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-primary text-white me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                        <i class="fas fa-award"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">Accredited Facility</h6>
                                        <small class="text-muted">Certified healthcare provider</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-success text-white me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">Expert Team</h6>
                                        <small class="text-muted">Qualified medical professionals</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="row g-4 fade-in">
                        <div class="col-md-6">
                            <div class="feature-card text-center">
                                <div class="feature-icon bg-primary text-white mx-auto">
                                    <i class="fas fa-hospital"></i>
                                </div>
                                <h5 class="fw-bold">Modern Facility</h5>
                                <p class="text-muted small">State-of-the-art medical equipment and comfortable patient areas</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card text-center">
                                <div class="feature-icon bg-success text-white mx-auto">
                                    <i class="fas fa-laptop-medical"></i>
                                </div>
                                <h5 class="fw-bold">Digital Health</h5>
                                <p class="text-muted small">Advanced digital health records and telemedicine capabilities</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card text-center">
                                <div class="feature-icon bg-warning text-white mx-auto">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <h5 class="fw-bold">24/7 Service</h5>
                                <p class="text-muted small">Round-the-clock emergency services and medical support</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-card text-center">
                                <div class="feature-icon bg-info text-white mx-auto">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <h5 class="fw-bold">Student Focus</h5>
                                <p class="text-muted small">Specialized healthcare services designed for university students</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4 text-white">Get In Touch</h2>
                    <p class="lead text-white opacity-75">
                        We're here to help with all your healthcare needs. Contact us anytime.
                    </p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="contact-card text-center fade-in">
                        <div class="feature-icon bg-white text-primary mx-auto mb-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5 class="text-white fw-bold mb-3">Visit Us</h5>
                        <p class="text-white opacity-75 mb-0">
                            Minna-Bida Road<br>
                            Minna, Niger State<br>
                            Nigeria
                        </p>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="contact-card text-center fade-in">
                        <div class="feature-icon bg-white text-success mx-auto mb-3">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5 class="text-white fw-bold mb-3">Call Us</h5>
                        <p class="text-white opacity-75 mb-2">
                            <strong>Emergency:</strong> 911<br>
                            <strong>General:</strong> +234 ************<br>
                            <strong>Appointments:</strong> +234 ************
                        </p>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="contact-card text-center fade-in">
                        <div class="feature-icon bg-white text-warning mx-auto mb-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h5 class="text-white fw-bold mb-3">Operating Hours</h5>
                        <p class="text-white opacity-75 mb-0">
                            <strong>Emergency:</strong> 24/7<br>
                            <strong>General Services:</strong><br>
                            Mon-Fri: 8:00 AM - 6:00 PM<br>
                            Sat-Sun: 9:00 AM - 4:00 PM
                        </p>
                    </div>
                </div>
            </div>

            <div class="row mt-5">
                <div class="col-lg-8 mx-auto">
                    <div class="contact-card fade-in">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-white fw-bold mb-3">Quick Contact</h5>
                                <form>
                                    <div class="mb-3">
                                        <input type="text" class="form-control" placeholder="Your Name" required>
                                    </div>
                                    <div class="mb-3">
                                        <input type="email" class="form-control" placeholder="Your Email" required>
                                    </div>
                                    <div class="mb-3">
                                        <textarea class="form-control" rows="4" placeholder="Your Message" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-white fw-bold mb-3">Emergency Services</h5>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon bg-danger text-white me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                        <i class="fas fa-ambulance"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-white fw-bold mb-1">24/7 Emergency</h6>
                                        <small class="text-white opacity-75">Immediate medical attention</small>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="feature-icon bg-info text-white me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                        <i class="fas fa-phone-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-white fw-bold mb-1">Telehealth</h6>
                                        <small class="text-white opacity-75">Remote consultations available</small>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-success text-white me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                    <div>
                                        <h6 class="text-white fw-bold mb-1">Specialist Care</h6>
                                        <small class="text-white opacity-75">Expert medical specialists</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background change on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });

        // Contact form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We will get back to you soon.');
            this.reset();
        });

        // Emergency contact function
        function showEmergencyInfo() {
            alert('🚨 EMERGENCY CONTACT 🚨\n\n' +
                  '📞 Emergency Hotline: +234 ************\n' +
                  '🏥 Location: Minna-Bida Road, Minna, Niger State\n\n' +
                  '⚡ Available 24/7 for medical emergencies\n' +
                  '🚑 Ambulance services available\n\n' +
                  'For life-threatening emergencies, call immediately!');
        }
    </script>
</body>
</html>
