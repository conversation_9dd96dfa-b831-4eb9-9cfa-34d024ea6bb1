<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Dashboard - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
    <style>
        /* Comprehensive stability fixes */
        html, body {
            overflow-x: hidden;
            position: relative;
        }

        .container-fluid {
            position: relative;
            width: 100%;
            max-width: 100%;
            padding-right: 15px;
            padding-left: 15px;
        }

        .row {
            margin-right: 0;
            margin-left: 0;
        }

        .col-md-9, .col-lg-10 {
            position: relative;
            padding-right: 15px;
            padding-left: 15px;
        }

        /* Prevent all animations that might cause shaking */
        *, *::before, *::after {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-perspective: 1000;
            perspective: 1000;
        }

        /* Disable problematic transitions */
        .dashboard-card {
            transition: none !important;
            transform: none !important;
        }

        .dashboard-card:hover {
            transform: none !important;
        }

        /* Stable sidebar */
        .sidebar {
            position: relative;
            height: auto;
            overflow: visible;
        }

        /* Stable content sections */
        .content-section {
            min-height: 500px;
            position: relative;
            display: block;
        }

        /* Remove all transitions that might cause issues */
        .nav-link, .btn, .card {
            transition: none !important;
            animation: none !important;
        }

        /* Force hardware acceleration for stability */
        .main-content {
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }

        /* Prevent layout shifts */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        /* Stable modal positioning */
        .modal {
            position: fixed !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-warning fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold text-dark" href="index-redesigned.html">
                <i class="fas fa-pills me-2"></i>
                Newgate Hospital - Pharmacy Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-dark" href="#" id="pharmacistDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-md me-2"></i>
                        <span id="pharmacistName">Pharmacist</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="fas fa-user me-2"></i>My Profile
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="profilePictureManager.showProfilePictureModal()">
                            <i class="fas fa-camera me-2"></i>Change Picture
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-5 pt-3">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar bg-light">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column sidebar-nav">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showDashboard()">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showMedicines()">
                                <i class="fas fa-pills me-2"></i>Medicine Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPrescriptions()">
                                <i class="fas fa-prescription me-2"></i>Prescriptions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showDispensing()">
                                <i class="fas fa-hand-holding-medical me-2"></i>Dispensing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showInventory()">
                                <i class="fas fa-boxes me-2"></i>Stock Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSuppliers()">
                                <i class="fas fa-truck me-2"></i>Suppliers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showReports()">
                                <i class="fas fa-chart-bar me-2"></i>Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showMessages()">
                                <i class="fas fa-envelope me-2"></i>Messages
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ms-sm-auto px-md-4">
                <!-- Dashboard Content -->
                <div id="dashboardContent" class="content-section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2 fw-bold text-dark">
                            <i class="fas fa-pills me-2 text-warning"></i>
                            Pharmacy Dashboard
                        </h1>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="currentDate"></span>
                        </div>
                    </div>

                    <!-- Welcome Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-gradient-warning text-dark">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h4 class="mb-2">Welcome back, <span id="welcomeName">Pharmacist</span>!</h4>
                                            <p class="mb-0 opacity-90">
                                                <i class="fas fa-building me-2"></i>
                                                Department: <span id="pharmacistDepartment">Main Pharmacy</span> | 
                                                <i class="fas fa-user-tag me-2 ms-3"></i>
                                                Position: <span id="pharmacistPosition">Chief Pharmacist</span>
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-center">
                                            <div class="pharmacist-avatar">
                                                <img id="dashboardAvatar" src="https://cdn-icons-png.flaticon.com/128/2785/2785491.png" alt="Pharmacist Avatar" class="rounded-circle bg-white p-2 profile-picture" style="width: 80px; height: 80px; object-fit: cover;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row g-4 mb-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-pills fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="totalMedicines">245</h3>
                                    <p class="mb-0">Total Medicines</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-prescription fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="pendingPrescriptions">18</h3>
                                    <p class="mb-0">Pending Prescriptions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="lowStockItems">12</h3>
                                    <p class="mb-0">Low Stock Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card dashboard-card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-hand-holding-medical fa-2x mb-3"></i>
                                    <h3 class="mb-1" id="dispensedToday">35</h3>
                                    <p class="mb-0">Dispensed Today</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-bolt me-2 text-warning"></i>Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <button class="btn btn-outline-primary w-100" onclick="dispenseMedicine()">
                                                <i class="fas fa-hand-holding-medical me-2"></i>Dispense Medicine
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-success w-100" onclick="addNewMedicine()">
                                                <i class="fas fa-plus me-2"></i>Add Medicine
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-info w-100" onclick="checkStock()">
                                                <i class="fas fa-boxes me-2"></i>Check Stock
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-outline-warning w-100" onclick="generateReport()">
                                                <i class="fas fa-chart-bar me-2"></i>Generate Report
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-clock me-2 text-info"></i>Recent Activities</h5>
                                </div>
                                <div class="card-body">
                                    <div class="activity-item d-flex align-items-center mb-3">
                                        <div class="activity-icon bg-success text-white rounded-circle me-3">
                                            <i class="fas fa-hand-holding-medical"></i>
                                        </div>
                                        <div>
                                            <strong>Paracetamol dispensed</strong> to Patient 22A/UE/BSE/1001
                                            <br><small class="text-muted">5 minutes ago</small>
                                        </div>
                                    </div>
                                    <div class="activity-item d-flex align-items-center mb-3">
                                        <div class="activity-icon bg-info text-white rounded-circle me-3">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div>
                                            <strong>New stock added</strong> - Amoxicillin 500mg
                                            <br><small class="text-muted">20 minutes ago</small>
                                        </div>
                                    </div>
                                    <div class="activity-item d-flex align-items-center">
                                        <div class="activity-icon bg-warning text-white rounded-circle me-3">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div>
                                            <strong>Low stock alert</strong> - Insulin injection
                                            <br><small class="text-muted">1 hour ago</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Low Stock Alerts -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Low Stock Alerts</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Medicine</th>
                                                    <th>Current Stock</th>
                                                    <th>Minimum Level</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><strong>Insulin Injection</strong></td>
                                                    <td>5 units</td>
                                                    <td>20 units</td>
                                                    <td><span class="badge bg-danger">Critical</span></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="reorderMedicine('insulin')">
                                                            <i class="fas fa-shopping-cart"></i> Reorder
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Paracetamol 500mg</strong></td>
                                                    <td>15 boxes</td>
                                                    <td>25 boxes</td>
                                                    <td><span class="badge bg-warning">Low</span></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="reorderMedicine('paracetamol')">
                                                            <i class="fas fa-shopping-cart"></i> Reorder
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Amoxicillin 250mg</strong></td>
                                                    <td>8 boxes</td>
                                                    <td>15 boxes</td>
                                                    <td><span class="badge bg-warning">Low</span></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="reorderMedicine('amoxicillin')">
                                                            <i class="fas fa-shopping-cart"></i> Reorder
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other content sections will be loaded dynamically -->
                <div id="medicinesContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading medicine inventory...</p>
                    </div>
                </div>

                <div id="prescriptionsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading prescriptions...</p>
                    </div>
                </div>

                <div id="dispensingContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading dispensing...</p>
                    </div>
                </div>

                <div id="inventoryContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading inventory...</p>
                    </div>
                </div>

                <div id="suppliersContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading suppliers...</p>
                    </div>
                </div>

                <div id="reportsContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-secondary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading reports...</p>
                    </div>
                </div>

                <div id="messagesContent" class="content-section" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-dark" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading messages...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/pharmacy-login.js"></script>
    <script src="js/profile-picture-manager.js"></script>
    <script src="js/pharmacy-dashboard.js"></script>
</body>
</html>
