/* Newgate Hospital Management System - Enhanced Custom Styles */

/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-info: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-danger: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    font-weight: 400;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

.display-4 {
    font-weight: 700;
}

/* Top Bar */
.top-bar {
    font-size: 0.875rem;
}

/* Logo Styles */
.logo-icon {
    transition: all 0.3s ease;
}

.navbar-brand:hover .logo-icon {
    transform: scale(1.1);
}

/* Navigation Styles */
.navbar {
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f8ff 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(13, 110, 253, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(25, 135, 84, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-content .lead {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    font-weight: 400;
}

/* Hero Image Styles */
.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    z-index: 1;
}

.hero-main-image {
    position: relative;
    z-index: 2;
    padding: 2rem;
}

.hero-illustration {
    position: relative;
    display: inline-block;
}

.main-hero-img {
    max-width: 200px;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.main-hero-img:hover {
    transform: scale(1.05);
}

/* Pulse Dots */
.pulse-dot {
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.pulse-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.pulse-2 {
    top: 60%;
    right: 15%;
    animation-delay: 0.7s;
}

.pulse-3 {
    bottom: 25%;
    left: 20%;
    animation-delay: 1.4s;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* Floating Cards */
.floating-card {
    position: absolute;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    animation: float 3s ease-in-out infinite;
    z-index: 3;
    transition: all 0.3s ease;
}

.floating-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.floating-card .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card .card-icon img {
    transition: transform 0.3s ease;
}

.floating-card:hover .card-icon img {
    transform: scale(1.1);
}

.card-1 {
    top: 20%;
    left: -10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: -10%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 20%;
    left: 10%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Card Image Styles */
.card-image-header {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.card-main-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.card-main-image:hover {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: -5px;
    right: -5px;
    background: white;
    border-radius: 50%;
    padding: 8px;
    box-shadow: var(--shadow-sm);
    opacity: 0;
    transition: all 0.3s ease;
}

.card-image-header:hover .image-overlay {
    opacity: 1;
    transform: scale(1.1);
}

.overlay-icon img {
    width: 24px;
    height: 24px;
}

/* Patient Hero Image */
.patient-hero-image {
    position: relative;
    display: inline-block;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-icon {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 8px;
    box-shadow: var(--shadow-sm);
    animation: floatIcon 3s ease-in-out infinite;
}

.icon-1 {
    top: 10%;
    left: -20%;
    animation-delay: 0s;
}

.icon-2 {
    bottom: 20%;
    right: -15%;
    animation-delay: 1.5s;
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(5deg); }
}

/* Service Card Styles */
.service-card {
    transition: all 0.3s ease;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-image-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.service-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.service-card:hover .service-image {
    transform: scale(1.1) rotate(5deg);
}

.service-bg-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border-radius: 50%;
    z-index: 1;
    transition: all 0.3s ease;
}

.service-card:hover .service-bg-circle {
    transform: translate(-50%, -50%) scale(1.2);
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.2) 0%, rgba(25, 135, 84, 0.2) 100%);
}

.service-features {
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

/* Statistics Section */
.stats-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.stat-card {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-10px);
}

.stat-image-container {
    position: relative;
    display: inline-block;
}

.stat-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.stat-card:hover .stat-image {
    transform: scale(1.2) rotate(10deg);
}

.stat-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 1;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-glow {
    transform: translate(-50%, -50%) scale(1.5);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-label {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
    color: white;
}

/* Testimonials Section */
.testimonial-card {
    transition: all 0.3s ease;
    border-radius: 20px;
    overflow: hidden;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.testimonial-image-container {
    position: relative;
    display: inline-block;
}

.testimonial-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover .testimonial-image {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.quote-icon {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.testimonial-text {
    font-style: italic;
    line-height: 1.6;
    color: #555;
}

.testimonial-rating {
    margin: 1rem 0;
}

.testimonial-name {
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.access-card {
    height: 100%;
    overflow: hidden;
    position: relative;
}

.access-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.access-card:hover::before {
    opacity: 1;
}

.access-card .card-body {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.icon-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
    overflow: hidden;
}

.icon-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
    transform: scale(1.5);
    border-radius: 50%;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-info {
    background: var(--gradient-info) !important;
}

.bg-gradient-danger {
    background: var(--gradient-danger) !important;
}

/* Dashboard Styles */
.dashboard-card {
    border-radius: 15px;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.sidebar {
    background-color: #f8f9fa;
    min-height: calc(100vh - 76px);
    padding: 2rem 0;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--dark-color);
    text-decoration: none;
    border-radius: 8px;
    margin: 0 1rem;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background-color: var(--primary-color);
    color: white;
}

/* Form Styles */
.form-control {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 0.875rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

/* Enhanced Button Styles */
.btn {
    border-radius: 12px;
    font-weight: 500;
    padding: 0.875rem 1.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.btn-primary {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

/* Dropdown Enhancements */
.dropdown-menu {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow-lg);
    padding: 1rem 0;
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0;
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateX(5px);
}

.dropdown-header {
    font-weight: 600;
    color: var(--primary-color);
    padding: 0.5rem 1.5rem;
}

/* Login Page Styles */
.login-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

/* Footer Styles */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-content h1 {
        font-size: 3rem;
    }

    .floating-card {
        display: none;
    }

    .hero-image-bg {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        display: none !important;
    }

    .hero-content h1 {
        font-size: 2.5rem;
        text-align: center;
    }

    .hero-content .lead {
        font-size: 1.1rem;
        text-align: center;
    }

    .hero-content .d-flex {
        justify-content: center;
    }

    .sidebar {
        min-height: auto;
        padding: 1rem 0;
    }

    .access-card .card-body {
        padding: 1.5rem;
    }

    .icon-circle {
        width: 70px;
        height: 70px;
    }

    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-content .lead {
        font-size: 1rem;
    }

    .card-body {
        padding: 1.25rem !important;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem !important;
    }

    .navbar-brand {
        font-size: 1rem;
    }

    .logo-icon {
        width: 35px;
        height: 35px;
    }

    .floating-card {
        position: static;
        margin: 0.5rem 0;
        animation: none;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Interactive Effects */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Navbar Scroll Effect */
.navbar-scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Hover Effects */
.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(13, 110, 253, 0.3);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-card {
        animation: none;
    }
}

/* Focus Styles */
.btn:focus,
.form-control:focus,
.dropdown-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .floating-card {
        display: none !important;
    }

    .hero-section {
        background: white !important;
        color: black !important;
    }
}
