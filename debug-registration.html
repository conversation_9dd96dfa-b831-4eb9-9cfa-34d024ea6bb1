<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Registration - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="mb-0"><i class="fas fa-bug me-2"></i>Registration Debug Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Debug Instructions</h5>
                            <p class="mb-2">This simplified form will help us test the registration functionality:</p>
                            <ol class="mb-0">
                                <li>Fill out the form below with test data</li>
                                <li>Open browser console (F12) to see debug messages</li>
                                <li>Click "Test Registration" to submit</li>
                                <li>Check console for any errors</li>
                            </ol>
                        </div>
                        
                        <form id="debugRegistrationForm" novalidate>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" value="Test" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" value="User" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="+234-************" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="dateOfBirth" class="form-label">Date of Birth *</label>
                                    <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth" value="1995-01-01" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label">Gender *</label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="male" selected>Male</option>
                                        <option value="female">Female</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="address" class="form-label">Address *</label>
                                    <input type="text" class="form-control" id="address" name="address" value="123 Test Street" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="city" class="form-label">City *</label>
                                    <input type="text" class="form-control" id="city" name="city" value="Lagos" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="state" class="form-label">State *</label>
                                    <input type="text" class="form-control" id="state" name="state" value="Lagos" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="emergencyName" class="form-label">Emergency Contact *</label>
                                    <input type="text" class="form-control" id="emergencyName" name="emergencyName" value="Emergency Contact" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="emergencyPhone" class="form-label">Emergency Phone *</label>
                                    <input type="tel" class="form-control" id="emergencyPhone" name="emergencyPhone" value="+234-************" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="emergencyRelation" class="form-label">Relationship *</label>
                                    <select class="form-select" id="emergencyRelation" name="emergencyRelation" required>
                                        <option value="">Select Relationship</option>
                                        <option value="parent" selected>Parent</option>
                                        <option value="spouse">Spouse</option>
                                        <option value="sibling">Sibling</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" value="test123456" required minlength="8">
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="termsAccepted" name="termsAccepted" checked required>
                                        <label class="form-check-label" for="termsAccepted">
                                            I agree to the terms and conditions *
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-warning btn-lg w-100">
                                    <i class="fas fa-bug me-2"></i>Test Registration
                                </button>
                            </div>
                        </form>
                        
                        <div id="debugOutput" class="mt-4"></div>
                        
                        <div class="mt-4">
                            <h6>Debug Actions:</h6>
                            <button class="btn btn-outline-info btn-sm me-2" onclick="checkFormElements()">
                                <i class="fas fa-search me-1"></i>Check Form Elements
                            </button>
                            <button class="btn btn-outline-success btn-sm me-2" onclick="testFormData()">
                                <i class="fas fa-data me-1"></i>Test Form Data
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="clearConsole()">
                                <i class="fas fa-trash me-1"></i>Clear Console
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple registration test function
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
            
            const form = document.getElementById('debugRegistrationForm');
            if (form) {
                console.log('Debug form found');
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    console.log('Debug form submitted');
                    
                    const formData = new FormData(form);
                    console.log('Form data collected:');
                    
                    const data = {};
                    for (let [key, value] of formData.entries()) {
                        console.log(key + ': ' + value);
                        data[key] = value;
                    }
                    
                    // Test patient ID generation
                    const existingPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
                    console.log('Existing patients:', existingPatients);
                    
                    const patientId = generateTestPatientId();
                    console.log('Generated Patient ID:', patientId);
                    
                    // Create patient data
                    const patientData = {
                        id: patientId,
                        firstName: data.firstName,
                        lastName: data.lastName,
                        name: data.firstName + ' ' + data.lastName,
                        email: data.email,
                        phone: data.phone,
                        dateOfBirth: data.dateOfBirth,
                        gender: data.gender,
                        address: data.address,
                        city: data.city,
                        state: data.state,
                        emergencyContactName: data.emergencyName,
                        emergencyContactPhone: data.emergencyPhone,
                        emergencyContactRelation: data.emergencyRelation,
                        password: data.password,
                        registrationDate: new Date().toISOString()
                    };
                    
                    console.log('Patient data created:', patientData);
                    
                    // Save to localStorage
                    try {
                        existingPatients[patientId] = patientData;
                        localStorage.setItem('registeredPatients', JSON.stringify(existingPatients));
                        console.log('Patient data saved successfully');
                        
                        document.getElementById('debugOutput').innerHTML = `
                            <div class="alert alert-success">
                                <h6>Registration Test Successful!</h6>
                                <p><strong>Patient ID:</strong> ${patientId}</p>
                                <p><strong>Name:</strong> ${patientData.name}</p>
                                <p><strong>Email:</strong> ${patientData.email}</p>
                                <p>Check console for detailed logs.</p>
                            </div>
                        `;
                        
                    } catch (error) {
                        console.error('Error saving patient data:', error);
                        document.getElementById('debugOutput').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>Registration Test Failed!</h6>
                                <p>Error: ${error.message}</p>
                            </div>
                        `;
                    }
                });
            } else {
                console.error('Debug form not found');
            }
        });
        
        function generateTestPatientId() {
            const existingPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const existingIds = Object.keys(existingPatients);
            let maxNumber = 8; // Start after existing PT008
            
            existingIds.forEach(id => {
                if (id.startsWith('PT')) {
                    const number = parseInt(id.substring(2));
                    if (number > maxNumber) {
                        maxNumber = number;
                    }
                }
            });
            
            const newNumber = maxNumber + 1;
            return `PT${newNumber.toString().padStart(3, '0')}`;
        }
        
        function checkFormElements() {
            console.log('=== Form Elements Check ===');
            const form = document.getElementById('debugRegistrationForm');
            const elements = form.elements;
            
            for (let i = 0; i < elements.length; i++) {
                const element = elements[i];
                console.log(`${element.name}: ${element.value} (type: ${element.type})`);
            }
        }
        
        function testFormData() {
            console.log('=== Form Data Test ===');
            const form = document.getElementById('debugRegistrationForm');
            const formData = new FormData(form);
            
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }
        }
        
        function clearConsole() {
            console.clear();
            console.log('Console cleared - ready for new test');
        }
    </script>
</body>
</html>
