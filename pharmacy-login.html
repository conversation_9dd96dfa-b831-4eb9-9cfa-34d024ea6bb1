<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Login - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-warning text-dark text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-pills fa-3x text-dark"></i>
                        </div>
                        <h2 class="fw-bold mb-2">Pharmacy Portal</h2>
                        <p class="mb-0">Newgate Hospital Management System</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h4 class="text-dark mb-2">Welcome Back</h4>
                            <p class="text-muted">Please sign in to your pharmacy account</p>
                        </div>

                        <form id="pharmacyLoginForm" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label for="pharmacistId" class="form-label fw-semibold">
                                    <i class="fas fa-user-tag me-2 text-warning"></i>Pharmacist ID
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-warning text-dark">
                                        <i class="fas fa-id-badge"></i>
                                    </span>
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="pharmacistId" 
                                           name="pharmacistId" 
                                           required 
                                           placeholder="Enter your Pharmacist ID"
                                           autocomplete="username">
                                </div>
                                <div class="invalid-feedback">
                                    Please enter your Pharmacist ID.
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="loginPassword" class="form-label fw-semibold">
                                    <i class="fas fa-lock me-2 text-warning"></i>Password
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-warning text-dark">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control form-control-lg" 
                                           id="loginPassword" 
                                           name="password" 
                                           required 
                                           placeholder="Enter your password"
                                           autocomplete="current-password">
                                </div>
                                <div class="invalid-feedback">
                                    Please enter your password.
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                                    <label class="form-check-label text-muted" for="rememberMe">
                                        Remember me for 30 days
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-warning btn-lg text-dark fw-bold">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In to Pharmacy
                                </button>
                            </div>
                        </form>

                        <!-- Error/Success Messages -->
                        <div id="errorMessage" class="mt-3"></div>

                        <hr class="my-4">

                        <!-- Sample Credentials -->
                        <div id="sampleCredentials"></div>

                        <div class="text-center">
                            <p class="text-muted mb-3">Need help accessing your account?</p>
                            <a href="#" class="btn btn-outline-warning" onclick="contactIT()">
                                <i class="fas fa-headset me-2"></i>Contact IT Support
                            </a>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-light text-center py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Secure Login Portal
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-home me-1"></i>
                                    <a href="index-redesigned.html" class="text-decoration-none">Back to Home</a>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-4">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="text-muted mb-2">
                        <i class="fas fa-hospital-alt me-2"></i>
                        <strong>Newgate Hospital Management System</strong>
                    </p>
                    <p class="text-muted small mb-0">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        Minna-Bida Road, Minna, Niger State | 
                        <i class="fas fa-phone me-1"></i>
                        +234 ************
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/pharmacy-login.js"></script>
    <script>
        // Contact IT Support function
        function contactIT() {
            alert('IT Support Contact:\nPhone: +234 ************\nEmail: <EMAIL>\n\nFor pharmacy access issues, please contact the IT department during business hours (8:00 AM - 5:00 PM).');
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
