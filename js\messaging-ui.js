// Universal Messaging UI Components
// Provides messaging interface for all user types

class MessagingUI {
    constructor() {
        this.currentUser = null;
        this.selectedMessage = null;
        this.messageModal = null;
    }

    // Initialize messaging UI for current user
    initialize(user) {
        this.currentUser = user;
        messagingSystem.initialize(user);
        
        // Create sample messages if none exist
        messagingSystem.createSampleMessages();
        
        // Update message count in UI
        this.updateMessageCount();
        
        // Add message listener
        messagingSystem.addListener((event, data) => {
            this.handleMessageEvent(event, data);
        });
    }

    // Show messaging modal
    showMessagingModal() {
        this.createMessagingModal();
        this.messageModal.show();
        this.loadMessageList();
    }

    // Create messaging modal
    createMessagingModal() {
        // Remove existing modal
        const existingModal = document.getElementById('messagingModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modalHTML = `
            <div class="modal fade" id="messagingModal" tabindex="-1" aria-labelledby="messagingModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="messagingModalLabel">
                                <i class="fas fa-envelope me-2"></i>Messages
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="row g-0" style="height: 600px;">
                                <!-- Message List -->
                                <div class="col-md-4 border-end">
                                    <div class="p-3 border-bottom bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Messages</h6>
                                            <button class="btn btn-primary btn-sm" onclick="messagingUI.showComposeModal()">
                                                <i class="fas fa-plus me-1"></i>New
                                            </button>
                                        </div>
                                        <div class="mt-2">
                                            <input type="text" class="form-control form-control-sm" placeholder="Search messages..." 
                                                   onkeyup="messagingUI.searchMessages(this.value)">
                                        </div>
                                    </div>
                                    <div id="messageList" class="overflow-auto" style="height: calc(100% - 100px);">
                                        <!-- Messages will be loaded here -->
                                    </div>
                                </div>
                                
                                <!-- Message Content -->
                                <div class="col-md-8">
                                    <div id="messageContent" class="h-100 d-flex align-items-center justify-content-center text-muted">
                                        <div class="text-center">
                                            <i class="fas fa-envelope fa-3x mb-3"></i>
                                            <p>Select a message to view its content</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.messageModal = new bootstrap.Modal(document.getElementById('messagingModal'));
    }

    // Load message list
    loadMessageList(searchQuery = '') {
        const messageListContainer = document.getElementById('messageList');
        if (!messageListContainer) return;

        let messages;
        if (searchQuery) {
            messages = messagingSystem.searchMessages(searchQuery);
        } else {
            messages = messagingSystem.getMessagesForUser();
        }

        if (messages.length === 0) {
            messageListContainer.innerHTML = `
                <div class="p-3 text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p class="mb-0">No messages found</p>
                </div>
            `;
            return;
        }

        const messageHTML = messages.map(message => {
            const isReceived = message.recipientId === this.currentUser.id;
            const otherParty = isReceived ? message.senderName : `To: ${this.getRecipientName(message.recipientId, message.recipientRole)}`;
            const timeAgo = this.getTimeAgo(message.timestamp);
            const priorityClass = this.getPriorityClass(message.priority);
            
            return `
                <div class="message-item p-3 border-bottom ${!message.read && isReceived ? 'bg-light' : ''}" 
                     onclick="messagingUI.selectMessage('${message.id}')" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <strong class="me-2">${otherParty}</strong>
                                ${!message.read && isReceived ? '<span class="badge bg-primary">New</span>' : ''}
                                ${message.replied ? '<i class="fas fa-reply text-muted ms-1"></i>' : ''}
                                ${priorityClass ? `<span class="badge ${priorityClass} ms-1">${message.priority}</span>` : ''}
                            </div>
                            <div class="text-truncate fw-bold">${message.subject}</div>
                            <div class="text-truncate text-muted small">${message.content}</div>
                        </div>
                        <small class="text-muted">${timeAgo}</small>
                    </div>
                </div>
            `;
        }).join('');

        messageListContainer.innerHTML = messageHTML;
    }

    // Select and display message
    selectMessage(messageId) {
        const message = messagingSystem.messages.find(m => m.id === messageId);
        if (!message) return;

        this.selectedMessage = message;

        // Mark as read if it's a received message
        if (message.recipientId === this.currentUser.id && !message.read) {
            messagingSystem.markAsRead(messageId);
            this.updateMessageCount();
            this.loadMessageList(); // Refresh list to update read status
        }

        this.displayMessage(message);
    }

    // Display message content
    displayMessage(message) {
        const messageContentContainer = document.getElementById('messageContent');
        if (!messageContentContainer) return;

        const isReceived = message.recipientId === this.currentUser.id;
        const otherParty = isReceived ? message.senderName : this.getRecipientName(message.recipientId, message.recipientRole);
        const timeFormatted = new Date(message.timestamp).toLocaleString();
        const priorityClass = this.getPriorityClass(message.priority);

        const messageHTML = `
            <div class="p-4 h-100 d-flex flex-column">
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="mb-1">${message.subject}</h5>
                            <div class="text-muted">
                                <strong>${isReceived ? 'From' : 'To'}:</strong> ${otherParty}
                                ${priorityClass ? `<span class="badge ${priorityClass} ms-2">${message.priority}</span>` : ''}
                            </div>
                            <small class="text-muted">${timeFormatted}</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                ${isReceived ? `<li><a class="dropdown-item" href="#" onclick="messagingUI.showReplyModal('${message.id}')">
                                    <i class="fas fa-reply me-2"></i>Reply
                                </a></li>` : ''}
                                <li><a class="dropdown-item" href="#" onclick="messagingUI.forwardMessage('${message.id}')">
                                    <i class="fas fa-share me-2"></i>Forward
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="messagingUI.deleteMessage('${message.id}')">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="flex-grow-1 overflow-auto">
                    <div class="message-content">
                        ${message.content.replace(/\n/g, '<br>')}
                    </div>
                </div>
                
                ${isReceived ? `
                <div class="border-top pt-3 mt-3">
                    <button class="btn btn-primary" onclick="messagingUI.showReplyModal('${message.id}')">
                        <i class="fas fa-reply me-2"></i>Reply
                    </button>
                </div>
                ` : ''}
            </div>
        `;

        messageContentContainer.innerHTML = messageHTML;
    }

    // Show compose message modal
    showComposeModal() {
        this.createComposeModal();
    }

    // Show reply modal
    showReplyModal(messageId) {
        const originalMessage = messagingSystem.messages.find(m => m.id === messageId);
        if (!originalMessage) return;

        this.createComposeModal(originalMessage);
    }

    // Create compose/reply modal
    createComposeModal(replyToMessage = null) {
        const isReply = !!replyToMessage;
        const modalId = isReply ? 'replyModal' : 'composeModal';
        
        // Remove existing modal
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const users = messagingSystem.getAllUsers().filter(u => 
            u.id !== this.currentUser.id || u.role !== this.currentUser.role
        );

        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-${isReply ? 'reply' : 'envelope'} me-2"></i>
                                ${isReply ? 'Reply to Message' : 'Compose Message'}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="${isReply ? 'replyForm' : 'composeForm'}">
                                ${!isReply ? `
                                <div class="mb-3">
                                    <label class="form-label">To *</label>
                                    <select class="form-select" name="recipient" required>
                                        <option value="">Select recipient...</option>
                                        ${users.map(user => `
                                            <option value="${user.id}|${user.role}">${user.name} (${user.role.charAt(0).toUpperCase() + user.role.slice(1)})</option>
                                        `).join('')}
                                    </select>
                                </div>
                                ` : `
                                <div class="mb-3">
                                    <label class="form-label">To</label>
                                    <input type="text" class="form-control" value="${replyToMessage.senderName} (${replyToMessage.senderRole})" readonly>
                                </div>
                                `}
                                
                                <div class="mb-3">
                                    <label class="form-label">Subject *</label>
                                    <input type="text" class="form-control" name="subject" required 
                                           value="${isReply ? 'Re: ' + replyToMessage.subject : ''}" 
                                           placeholder="Enter subject">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" name="priority">
                                        <option value="low">Low</option>
                                        <option value="normal" selected>Normal</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Message *</label>
                                    <textarea class="form-control" name="content" rows="6" required 
                                              placeholder="Type your message here..."></textarea>
                                </div>
                                
                                ${isReply ? `
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <small class="text-muted">Original Message:</small>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">
                                            <strong>From:</strong> ${replyToMessage.senderName}<br>
                                            <strong>Subject:</strong> ${replyToMessage.subject}<br>
                                            <strong>Date:</strong> ${new Date(replyToMessage.timestamp).toLocaleString()}<br><br>
                                            ${replyToMessage.content}
                                        </small>
                                    </div>
                                </div>
                                ` : ''}
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="messagingUI.${isReply ? 'sendReply' : 'sendMessage'}('${isReply ? replyToMessage.id : ''}')">
                                <i class="fas fa-paper-plane me-2"></i>Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    // Send new message
    sendMessage() {
        const form = document.getElementById('composeForm');
        const formData = new FormData(form);
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const [recipientId, recipientRole] = formData.get('recipient').split('|');
        
        try {
            messagingSystem.sendMessage(
                recipientId,
                recipientRole,
                formData.get('subject'),
                formData.get('content'),
                formData.get('priority')
            );

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('composeModal'));
            modal.hide();

            // Refresh message list
            this.loadMessageList();
            
            // Show success message
            this.showAlert('Message sent successfully!', 'success');
        } catch (error) {
            this.showAlert('Error sending message: ' + error.message, 'danger');
        }
    }

    // Send reply
    sendReply(originalMessageId) {
        const form = document.getElementById('replyForm');
        const formData = new FormData(form);
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        try {
            messagingSystem.replyToMessage(originalMessageId, formData.get('content'));

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('replyModal'));
            modal.hide();

            // Refresh message list
            this.loadMessageList();
            
            // Show success message
            this.showAlert('Reply sent successfully!', 'success');
        } catch (error) {
            this.showAlert('Error sending reply: ' + error.message, 'danger');
        }
    }

    // Search messages
    searchMessages(query) {
        this.loadMessageList(query);
    }

    // Update message count in UI
    updateMessageCount() {
        const unreadCount = messagingSystem.getUnreadCount();
        const messageCountElements = document.querySelectorAll('.message-count');
        
        messageCountElements.forEach(element => {
            if (unreadCount > 0) {
                element.textContent = unreadCount;
                element.style.display = 'inline';
            } else {
                element.style.display = 'none';
            }
        });
    }

    // Handle message events
    handleMessageEvent(event, data) {
        switch (event) {
            case 'messageSent':
            case 'messageReplied':
            case 'messageRead':
                this.updateMessageCount();
                break;
        }
    }

    // Helper methods
    getRecipientName(recipientId, recipientRole) {
        const users = messagingSystem.getAllUsers();
        const user = users.find(u => u.id === recipientId && u.role === recipientRole);
        return user ? user.name : 'Unknown User';
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const messageTime = new Date(timestamp);
        const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
        return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }

    getPriorityClass(priority) {
        switch (priority) {
            case 'urgent': return 'bg-danger';
            case 'high': return 'bg-warning';
            case 'low': return 'bg-secondary';
            default: return '';
        }
    }

    showAlert(message, type = 'info') {
        // Create alert if container exists
        const alertContainer = document.getElementById('alertContainer') || document.body;
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alertDiv);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// Create global messaging UI instance
const messagingUI = new MessagingUI();

// Make it globally available
window.messagingUI = messagingUI;
