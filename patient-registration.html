<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Registration - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index-redesigned.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index-redesigned.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="patient-login.html">Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Registration Form Section -->
    <section class="py-5 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                Patient Registration
                            </h2>
                            <p class="mb-0 mt-2">Create your account to access our services</p>
                        </div>
                        <div class="card-body p-5">
                            <form id="patientRegistrationForm" novalidate>
                                <!-- Student Information -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-graduation-cap me-2"></i>Student Information
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="matricNumber" class="form-label">Matric Number *</label>
                                            <input type="text" class="form-control" id="matricNumber" name="matricNumber"
                                                   placeholder="e.g., 22A/UE/BNS/1001, 22B/UE/BSE/1005, 23A/UE/LLB/1001" required>
                                            <div class="invalid-feedback">Please provide a valid matric number</div>
                                            <div class="form-text">Examples: 22A/UE/BNS/1001, 22B/UE/BSE/1005, 23A/UE/LLB/1001</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="fullName" class="form-label">Full Name *</label>
                                            <input type="text" class="form-control" id="fullName" name="fullName" required>
                                            <div class="invalid-feedback">Please provide your full name.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="dateOfBirth" class="form-label">Date of Birth *</label>
                                            <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth" required>
                                            <div class="invalid-feedback">Please provide your date of birth.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="gender" class="form-label">Gender *</label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="">Select Gender</option>
                                                <option value="male">Male</option>
                                                <option value="female">Female</option>
                                            </select>
                                            <div class="invalid-feedback">Please select your gender.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="department" class="form-label">Department *</label>
                                            <select class="form-select" id="department" name="department" required>
                                                <option value="">Select Department</option>
                                                <option value="BSE">Biomedical Science and Engineering (BSE)</option>
                                                <option value="BNS">Basic and Applied Sciences (BNS)</option>
                                                <option value="LLB">Law (LLB)</option>
                                                <option value="ENG">Engineering</option>
                                                <option value="EDU">Education</option>
                                                <option value="AGR">Agriculture</option>
                                                <option value="ENV">Environmental Sciences</option>
                                                <option value="ICT">Information and Communication Technology</option>
                                            </select>
                                            <div class="invalid-feedback">Please select your department.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="bloodGroup" class="form-label">Blood Group *</label>
                                            <select class="form-select" id="bloodGroup" name="bloodGroup" required>
                                                <option value="">Select Blood Group</option>
                                                <option value="A+">A+</option>
                                                <option value="A-">A-</option>
                                                <option value="B+">B+</option>
                                                <option value="B-">B-</option>
                                                <option value="AB+">AB+</option>
                                                <option value="AB-">AB-</option>
                                                <option value="O+">O+</option>
                                                <option value="O-">O-</option>
                                            </select>
                                            <div class="invalid-feedback">Please select your blood group.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-phone me-2"></i>Contact Information
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                            <div class="invalid-feedback">Please provide a valid email address.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="phone" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" required>
                                            <div class="invalid-feedback">Please provide a valid phone number.</div>
                                        </div>
                                    </div>
                                </div>



                                <!-- Account Security -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-lock me-2"></i>Account Security
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="password" class="form-label">Password *</label>
                                            <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                            <div class="invalid-feedback">Password must be at least 8 characters long.</div>
                                            <div class="form-text">Password must be at least 8 characters long.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="confirmPassword" class="form-label">Confirm Password *</label>
                                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                            <div class="invalid-feedback">Passwords do not match.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="termsAccepted" name="termsAccepted" required>
                                        <label class="form-check-label" for="termsAccepted">
                                            I agree to the <a href="#" class="text-primary">Terms and Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a> *
                                        </label>
                                        <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>Register Account
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="testRegistration()">
                                        <i class="fas fa-bug me-2"></i>Test Form (Debug)
                                    </button>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <p class="text-muted">Already have an account? <a href="patient-login.html" class="text-primary">Login here</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Alert -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="successAlert" class="alert alert-success alert-dismissible fade" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span id="successMessage">Registration successful!</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>

    <!-- Error Alert -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="errorAlert" class="alert alert-danger alert-dismissible fade" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span id="errorMessage">Registration failed!</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Final Year Project - Newgate University Minna</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/patient-registration.js"></script>
</body>
</html>
