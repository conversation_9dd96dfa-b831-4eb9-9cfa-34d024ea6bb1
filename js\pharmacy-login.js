// Pharmacy Login System - Newgate Hospital Management System

// Pre-configured pharmacist accounts
const pharmacistAccounts = {
    'PHAR001': {
        id: 'PHAR001',
        password: 'pharmacy123',
        name: 'Pharmacist <PERSON>',
        department: 'Main Pharmacy',
        email: 'ibrahim.yaku<PERSON>@newgate.edu.ng',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'PCN-001-2020',
        experience: '8 years',
        education: 'PharmD, University of Jos',
        role: 'pharmacist',
        status: 'active',
        lastLogin: null,
        specialization: 'Clinical Pharmacy',
        position: 'Chief Pharmacist'
    },
    'PHAR002': {
        id: 'PHAR002',
        password: 'pharmacy123',
        name: 'Pharmacist <PERSON><PERSON><PERSON>',
        department: 'Outpatient Pharmacy',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'PCN-002-2021',
        experience: '5 years',
        education: '<PERSON>armD, University of Maiduguri',
        role: 'pharmacist',
        status: 'active',
        lastLogin: null,
        specialization: 'Community Pharmacy',
        position: 'Senior Pharmacist'
    },
    'PHAR003': {
        id: 'PHAR003',
        password: 'pharmacy123',
        name: 'Pharmacist <PERSON>afor',
        department: 'Inpatient Pharmacy',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Night Shift',
        licenseNumber: 'PCN-003-2019',
        experience: '10 years',
        education: 'PharmD, University of Nigeria',
        role: 'pharmacist',
        status: 'active',
        lastLogin: null,
        specialization: 'Hospital Pharmacy',
        position: 'Senior Pharmacist'
    },
    'PHAR004': {
        id: 'PHAR004',
        password: 'pharmacy123',
        name: 'Pharmacist Zainab Aliyu',
        department: 'Emergency Pharmacy',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'PCN-004-2022',
        experience: '3 years',
        education: 'PharmD, Ahmadu Bello University',
        role: 'pharmacist',
        status: 'active',
        lastLogin: null,
        specialization: 'Emergency Pharmacy',
        position: 'Pharmacist'
    },
    'PHAR005': {
        id: 'PHAR005',
        password: 'pharmacy123',
        name: 'Pharmacist Samuel Adebayo',
        department: 'Pediatric Pharmacy',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'PCN-005-2020',
        experience: '6 years',
        education: 'PharmD, University of Ibadan',
        role: 'pharmacist',
        status: 'active',
        lastLogin: null,
        specialization: 'Pediatric Pharmacy',
        position: 'Senior Pharmacist'
    }
};

// Initialize pharmacy login system
document.addEventListener('DOMContentLoaded', function() {
    initializePharmacyLogin();
});

function initializePharmacyLogin() {
    const loginForm = document.getElementById('pharmacyLoginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handlePharmacyLogin);
    }
    
    // Add sample credentials display
    displaySampleCredentials();
    
    // Check if already logged in
    checkExistingSession();
}

function displaySampleCredentials() {
    const credentialsContainer = document.getElementById('sampleCredentials');
    if (credentialsContainer) {
        credentialsContainer.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="fas fa-info-circle me-2"></i>Sample Pharmacist Credentials</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Pharmacist ID:</strong> PHAR001<br>
                        <strong>Password:</strong> pharmacy123<br>
                        <strong>Name:</strong> Pharmacist Ibrahim Yakubu
                    </div>
                    <div class="col-md-6">
                        <strong>Department:</strong> Main Pharmacy<br>
                        <strong>Position:</strong> Chief Pharmacist<br>
                        <strong>Status:</strong> Active
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <strong>Other Available Pharmacists:</strong> PHAR002, PHAR003, PHAR004, PHAR005 (all use password: pharmacy123)
                </small>
            </div>
        `;
    }
}

function handlePharmacyLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const pharmacistId = formData.get('pharmacistId').trim().toUpperCase();
    const password = formData.get('password').trim();
    
    // Clear previous error messages
    clearErrorMessages();
    
    // Validate inputs
    if (!pharmacistId || !password) {
        showErrorMessage('Please enter both Pharmacist ID and password.');
        return;
    }
    
    // Check credentials
    if (validatePharmacistCredentials(pharmacistId, password)) {
        // Successful login
        const pharmacistData = pharmacistAccounts[pharmacistId];
        pharmacistData.loginTime = new Date().toISOString();
        
        // Store pharmacist session
        sessionStorage.setItem('hospitalUser', JSON.stringify(pharmacistData));
        localStorage.setItem('hospitalUser', JSON.stringify(pharmacistData));
        
        // Show success message
        showSuccessMessage(`Welcome back, ${pharmacistData.name}!`);
        
        // Redirect to stable pharmacy dashboard
        setTimeout(() => {
            window.location.href = 'pharmacy-dashboard-stable.html';
        }, 1500);
        
    } else {
        showErrorMessage('Invalid Pharmacist ID or password. Please try again.');
    }
}

function validatePharmacistCredentials(pharmacistId, password) {
    const pharmacist = pharmacistAccounts[pharmacistId];
    return pharmacist && pharmacist.password === password && pharmacist.status === 'active';
}

function checkExistingSession() {
    const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
    
    if (user.role === 'pharmacist' && user.status === 'active') {
        // User is already logged in as pharmacist, redirect to stable dashboard
        window.location.href = 'pharmacy-dashboard-stable.html';
    }
}

function showErrorMessage(message) {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

function showSuccessMessage(message) {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

function clearErrorMessages() {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = '';
    }
}

// Utility functions
function getAllPharmacists() {
    return pharmacistAccounts;
}

function isLoggedInAsPharmacist() {
    const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
    return user.role === 'pharmacist' && user.status === 'active';
}

function getCurrentPharmacist() {
    if (isLoggedInAsPharmacist()) {
        return JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser'));
    }
    return null;
}

function logoutPharmacist() {
    sessionStorage.removeItem('hospitalUser');
    localStorage.removeItem('hospitalUser');
    window.location.href = 'pharmacy-login.html';
}

// Make functions globally available
window.getAllPharmacists = getAllPharmacists;
window.isLoggedInAsPharmacist = isLoggedInAsPharmacist;
window.getCurrentPharmacist = getCurrentPharmacist;
window.logoutPharmacist = logoutPharmacist;
window.validatePharmacistCredentials = validatePharmacistCredentials;
