// Admin Login JavaScript

// Admin credentials (in a real application, this would be handled by a backend)
const adminCredentials = {
    'ADMIN001': { password: 'admin123', name: 'System Administrator', email: '<EMAIL>' },
    'ADMIN002': { password: 'admin456', name: 'IT Administrator', email: '<EMAIL>' },
    'SUPERADMIN': { password: 'super123', name: 'Super Administrator', email: '<EMAIL>' }
};

document.addEventListener('DOMContentLoaded', function() {
    initializeAdminLogin();
});

function initializeAdminLogin() {
    const loginForm = document.getElementById('adminLoginForm');
    const togglePasswordBtn = document.getElementById('togglePassword');
    
    // Handle form submission
    if (loginForm) {
        loginForm.addEventListener('submit', handleAdminLogin);
    }
    
    // Handle password toggle
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            togglePasswordVisibility('loginPassword', 'togglePassword');
        });
    }
    
    // Check if already logged in
    checkExistingSession();
}

function handleAdminLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const adminId = formData.get('adminId').trim().toUpperCase();
    const password = formData.get('loginPassword').trim();
    const rememberMe = formData.get('rememberMe');
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate API call delay
    setTimeout(() => {
        if (validateAdminCredentials(adminId, password)) {
            // Successful login
            const adminData = {
                id: adminId,
                name: adminCredentials[adminId].name,
                email: adminCredentials[adminId].email,
                role: 'admin',
                loginTime: new Date().toISOString()
            };
            
            // Store user data
            const storage = rememberMe ? localStorage : sessionStorage;
            storage.setItem('hospitalUser', JSON.stringify(adminData));
            
            // Show success message
            showSuccessAlert('Login successful! Redirecting to admin dashboard...');
            
            // Redirect to admin dashboard
            setTimeout(() => {
                window.location.href = 'admin-dashboard.html';
            }, 1500);
            
        } else {
            // Failed login
            showErrorAlert('Invalid Admin ID or password. Please try again.');
            form.classList.add('was-validated');
        }
        
        hideLoading();
    }, 1000);
}

function validateAdminCredentials(adminId, password) {
    return adminCredentials[adminId] && adminCredentials[adminId].password === password;
}

function checkExistingSession() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (userData) {
        const user = JSON.parse(userData);
        if (user.role === 'admin') {
            // User is already logged in as admin
            showSuccessAlert('You are already logged in. Redirecting...');
            setTimeout(() => {
                window.location.href = 'admin-dashboard.html';
            }, 1500);
        }
    }
}

function resetPassword() {
    const adminId = document.getElementById('resetAdminId').value.trim().toUpperCase();
    const email = document.getElementById('resetEmail').value.trim();
    
    if (!adminId || !email) {
        showErrorAlert('Please fill in all fields.');
        return;
    }
    
    if (!validateEmail(email)) {
        showErrorAlert('Please enter a valid email address.');
        return;
    }
    
    // Check if admin exists
    if (!adminCredentials[adminId]) {
        showErrorAlert('Admin ID not found.');
        return;
    }
    
    // Check if email matches
    if (adminCredentials[adminId].email !== email) {
        showErrorAlert('Email does not match our records.');
        return;
    }
    
    // Simulate sending reset email
    showSuccessAlert('Password reset link has been sent to your email address.');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
    modal.hide();
    
    // Clear form
    document.getElementById('forgotPasswordForm').reset();
}

// Toggle password visibility function
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Show success alert function
function showSuccessAlert(message) {
    const alert = document.getElementById('successAlert');
    const messageSpan = document.getElementById('successMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

// Show error alert function
function showErrorAlert(message) {
    const alert = document.getElementById('errorAlert');
    const messageSpan = document.getElementById('errorMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

// Show loading function
function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// Validate email function
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Make resetPassword function globally available
window.resetPassword = resetPassword;
