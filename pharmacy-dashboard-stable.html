<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Dashboard - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Ultra-stable CSS - No animations or transitions */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            height: 100%;
            overflow-x: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-fluid {
            padding: 0;
            margin: 0;
            width: 100%;
            max-width: 100%;
        }
        
        .sidebar {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px 0;
            border-right: 1px solid #dee2e6;
        }
        
        .main-content {
            padding: 20px;
            background: #ffffff;
            min-height: 100vh;
        }
        
        .nav-link {
            color: #495057;
            padding: 12px 20px;
            border: none;
            background: none;
            text-decoration: none;
            display: block;
            width: 100%;
            text-align: left;
        }
        
        .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }
        
        .nav-link.active {
            background-color: #ffc107;
            color: #000;
            font-weight: bold;
        }
        
        .card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .profile-dropdown {
            position: relative;
        }
        
        .alert {
            margin-bottom: 20px;
        }
        
        /* Remove all problematic animations */
        *, *::before, *::after {
            animation: none !important;
            transition: none !important;
            transform: none !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-warning">
        <div class="container-fluid">
            <a class="navbar-brand text-dark fw-bold" href="#">
                <i class="fas fa-pills me-2"></i>Pharmacy Dashboard
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="dropdown">
                    <button class="btn btn-outline-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i><span id="pharmacistName">Pharmacist</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showProfileSimple()">
                            <i class="fas fa-user me-2"></i>My Profile
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettingsSimple()">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('medicines')">
                        <i class="fas fa-pills me-2"></i>Medicine Inventory
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('prescriptions')">
                        <i class="fas fa-prescription me-2"></i>Prescriptions
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('dispensing')">
                        <i class="fas fa-hand-holding-medical me-2"></i>Dispensing
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('inventory')">
                        <i class="fas fa-boxes me-2"></i>Stock Management
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('suppliers')">
                        <i class="fas fa-truck me-2"></i>Suppliers
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('reports')">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Dashboard Section -->
                <div id="dashboard" class="content-section active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Dashboard</h2>
                        <span class="text-muted" id="currentDate"></span>
                    </div>

                    <!-- Welcome Card -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Welcome, <span id="welcomeName">Pharmacist</span>!</h5>
                            <p class="card-text">Manage medicines, prescriptions, and pharmacy operations efficiently.</p>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-hand-holding-medical fa-2x text-primary mb-2"></i>
                                    <h6>Dispense Medicine</h6>
                                    <button class="btn btn-primary btn-sm" onclick="dispenseMedicineSimple()">
                                        Quick Dispense
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-plus fa-2x text-success mb-2"></i>
                                    <h6>Add Medicine</h6>
                                    <button class="btn btn-success btn-sm" onclick="addMedicineSimple()">
                                        Add New
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-boxes fa-2x text-info mb-2"></i>
                                    <h6>Check Stock</h6>
                                    <button class="btn btn-info btn-sm" onclick="showSection('inventory')">
                                        View Stock
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-chart-bar fa-2x text-warning mb-2"></i>
                                    <h6>Generate Report</h6>
                                    <button class="btn btn-warning btn-sm" onclick="showSection('reports')">
                                        View Reports
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>150</h3>
                                    <p>Total Medicines</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>25</h3>
                                    <p>Prescriptions Today</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3>5</h3>
                                    <p>Low Stock Items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>₦45,000</h3>
                                    <p>Today's Sales</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medicine Inventory Section -->
                <div id="medicines" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Medicine Inventory</h2>
                        <button class="btn btn-success" onclick="showAddMedicineModal()">
                            <i class="fas fa-plus me-2"></i>Add Medicine
                        </button>
                    </div>
                    <div id="medicinesContent">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Category</th>
                                        <th>Stock</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="medicinesTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Prescriptions Section -->
                <div id="prescriptions" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Prescriptions</h2>
                        <select class="form-select w-auto">
                            <option>All Prescriptions</option>
                            <option>Pending</option>
                            <option>Dispensed</option>
                        </select>
                    </div>
                    <div id="prescriptionsContent">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Prescription ID</th>
                                        <th>Patient</th>
                                        <th>Doctor</th>
                                        <th>Medicine</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="prescriptionsTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Dispensing Section -->
                <div id="dispensing" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Medicine Dispensing</h2>
                        <button class="btn btn-primary" onclick="showDispenseModal()">
                            <i class="fas fa-hand-holding-medical me-2"></i>Quick Dispense
                        </button>
                    </div>
                    <div id="dispensingContent">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Today's Dispensing</h6>
                                        <h3 class="text-primary">25</h3>
                                        <small class="text-muted">Medicines dispensed</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Total Revenue</h6>
                                        <h3 class="text-success">₦45,000</h3>
                                        <small class="text-muted">Today's earnings</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Patient</th>
                                        <th>Medicine</th>
                                        <th>Quantity</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="dispensingTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Stock Management Section -->
                <div id="inventory" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Stock Management</h2>
                        <button class="btn btn-warning" onclick="showStockAdjustModal()">
                            <i class="fas fa-edit me-2"></i>Adjust Stock
                        </button>
                    </div>
                    <div id="inventoryContent">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4>150</h4>
                                        <p>Total Items</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h4>5</h4>
                                        <p>Low Stock</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4>145</h4>
                                        <p>In Stock</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4>₦2.5M</h4>
                                        <p>Total Value</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Current Stock</th>
                                        <th>Minimum Level</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="inventoryTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Suppliers Section -->
                <div id="suppliers" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Suppliers</h2>
                        <button class="btn btn-success" onclick="showAddSupplierModal()">
                            <i class="fas fa-plus me-2"></i>Add Supplier
                        </button>
                    </div>
                    <div id="suppliersContent">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Supplier</th>
                                        <th>Contact</th>
                                        <th>Products</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="suppliersTableBody">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Reports & Analytics</h2>
                        <button class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-download me-2"></i>Export Report
                        </button>
                    </div>
                    <div id="reportsContent">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Monthly Sales</h6>
                                        <h3 class="text-success">₦1,250,000</h3>
                                        <small class="text-muted">+15% from last month</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Medicines Dispensed</h6>
                                        <h3 class="text-info">2,450</h3>
                                        <small class="text-muted">This month</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <h6>Quick Reports</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="showAlert('Inventory report generated!', 'success')">
                                            <i class="fas fa-boxes me-2"></i>Inventory Report
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="showAlert('Sales report generated!', 'success')">
                                            <i class="fas fa-chart-line me-2"></i>Sales Report
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="showAlert('Expiry report generated!', 'success')">
                                            <i class="fas fa-calendar-times me-2"></i>Expiry Report
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="showAlert('Financial report generated!', 'success')">
                                            <i class="fas fa-money-bill me-2"></i>Financial Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple, stable JavaScript
        let currentPharmacist = null;

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeStableDashboard();
        });

        function initializeStableDashboard() {
            try {
                // Get user data
                const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
                
                if (!user.role || user.role !== 'pharmacist') {
                    window.location.href = 'pharmacy-login.html';
                    return;
                }
                
                currentPharmacist = user;
                
                // Update UI
                document.getElementById('pharmacistName').textContent = currentPharmacist.name || 'Pharmacist';
                document.getElementById('welcomeName').textContent = currentPharmacist.name || 'Pharmacist';
                document.getElementById('currentDate').textContent = new Date().toLocaleDateString();
                
                console.log('Stable pharmacy dashboard initialized');
            } catch (error) {
                console.error('Error:', error);
            }
        }

        // Section switching with data loading
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active from nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Add active to clicked nav link
            if (event && event.target) {
                event.target.classList.add('active');
            }

            // Load data for the section
            switch(sectionId) {
                case 'medicines':
                    loadMedicineData();
                    break;
                case 'prescriptions':
                    loadPrescriptionData();
                    break;
                case 'dispensing':
                    loadDispensingData();
                    break;
                case 'inventory':
                    loadInventoryData();
                    break;
                case 'suppliers':
                    loadSupplierData();
                    break;
                case 'reports':
                    // Reports section doesn't need data loading
                    break;
            }

            console.log('Switched to section:', sectionId);
        }

        // Simple alert function
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Medicine database
        const medicineDatabase = {
            'MED001': { id: 'MED001', name: 'Paracetamol', category: 'Analgesic', stock: 150, price: 50, status: 'Available' },
            'MED002': { id: 'MED002', name: 'Amoxicillin', category: 'Antibiotic', stock: 80, price: 120, status: 'Available' },
            'MED003': { id: 'MED003', name: 'Ibuprofen', category: 'NSAID', stock: 200, price: 75, status: 'Available' },
            'MED004': { id: 'MED004', name: 'Insulin', category: 'Antidiabetic', stock: 15, price: 500, status: 'Low Stock' },
            'MED005': { id: 'MED005', name: 'Metformin', category: 'Antidiabetic', stock: 100, price: 85, status: 'Available' }
        };

        // Action functions with full functionality
        function showProfileSimple() {
            const profileInfo = `
                <strong>Pharmacist Profile:</strong><br>
                Name: ${currentPharmacist ? currentPharmacist.name : 'Unknown'}<br>
                ID: ${currentPharmacist ? currentPharmacist.id : 'Unknown'}<br>
                Department: ${currentPharmacist ? currentPharmacist.department : 'Unknown'}<br>
                Position: ${currentPharmacist ? currentPharmacist.position : 'Unknown'}
            `;
            showAlert(profileInfo, 'info');
        }

        function showSettingsSimple() {
            const settingsInfo = `
                <strong>Settings Options:</strong><br>
                • Account Settings<br>
                • Security Settings<br>
                • Notification Preferences<br>
                • System Configuration
            `;
            showAlert(settingsInfo, 'info');
        }

        function dispenseMedicineSimple() {
            showDispenseModal();
        }

        function addMedicineSimple() {
            showAddMedicineModal();
        }

        function showDispenseModal() {
            const modalHTML = `
                <div class="modal fade" id="dispenseModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Dispense Medicine</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="dispenseForm">
                                    <div class="mb-3">
                                        <label class="form-label">Patient Matric Number</label>
                                        <input type="text" class="form-control" name="matricNumber" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Medicine</label>
                                        <select class="form-select" name="medicine" required>
                                            <option value="">Select Medicine</option>
                                            ${Object.values(medicineDatabase).map(med =>
                                                `<option value="${med.id}">${med.name} - ₦${med.price}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-control" name="quantity" min="1" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="processDispense()">Dispense</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            const existingModal = document.getElementById('dispenseModal');
            if (existingModal) existingModal.remove();

            // Add new modal
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = new bootstrap.Modal(document.getElementById('dispenseModal'));
            modal.show();
        }

        function showAddMedicineModal() {
            const modalHTML = `
                <div class="modal fade" id="addMedicineModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Add New Medicine</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="addMedicineForm">
                                    <div class="mb-3">
                                        <label class="form-label">Medicine Name</label>
                                        <input type="text" class="form-control" name="medicineName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Category</label>
                                        <select class="form-select" name="category" required>
                                            <option value="">Select Category</option>
                                            <option value="Analgesic">Analgesic</option>
                                            <option value="Antibiotic">Antibiotic</option>
                                            <option value="NSAID">NSAID</option>
                                            <option value="Antidiabetic">Antidiabetic</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Initial Stock</label>
                                        <input type="number" class="form-control" name="stock" min="0" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Unit Price (₦)</label>
                                        <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" onclick="addNewMedicine()">Add Medicine</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            const existingModal = document.getElementById('addMedicineModal');
            if (existingModal) existingModal.remove();

            // Add new modal
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = new bootstrap.Modal(document.getElementById('addMedicineModal'));
            modal.show();
        }

        function processDispense() {
            const form = document.getElementById('dispenseForm');
            const formData = new FormData(form);

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const medicineId = formData.get('medicine');
            const quantity = parseInt(formData.get('quantity'));
            const medicine = medicineDatabase[medicineId];

            if (medicine && medicine.stock >= quantity) {
                medicine.stock -= quantity;
                const total = medicine.price * quantity;

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('dispenseModal'));
                modal.hide();

                showAlert(`Medicine dispensed successfully! Total: ₦${total}`, 'success');

                // Update displays if on relevant sections
                loadMedicineData();
                loadDispensingData();
            } else {
                showAlert('Insufficient stock or invalid medicine selected!', 'danger');
            }
        }

        function addNewMedicine() {
            const form = document.getElementById('addMedicineForm');
            const formData = new FormData(form);

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const newId = 'MED' + String(Object.keys(medicineDatabase).length + 1).padStart(3, '0');
            medicineDatabase[newId] = {
                id: newId,
                name: formData.get('medicineName'),
                category: formData.get('category'),
                stock: parseInt(formData.get('stock')),
                price: parseFloat(formData.get('price')),
                status: 'Available'
            };

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addMedicineModal'));
            modal.hide();

            showAlert(`Medicine "${formData.get('medicineName')}" added successfully!`, 'success');

            // Update displays
            loadMedicineData();
        }

        function loadMedicineData() {
            const tbody = document.getElementById('medicinesTableBody');
            if (!tbody) return;

            tbody.innerHTML = Object.values(medicineDatabase).map(med => `
                <tr>
                    <td><strong>${med.name}</strong></td>
                    <td>${med.category}</td>
                    <td>${med.stock}</td>
                    <td>₦${med.price}</td>
                    <td><span class="badge bg-${med.stock < 20 ? 'warning' : 'success'}">${med.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showAlert('Viewing ${med.name} details', 'info')">View</button>
                        <button class="btn btn-sm btn-warning" onclick="showAlert('Editing ${med.name}', 'info')">Edit</button>
                    </td>
                </tr>
            `).join('');
        }

        function loadPrescriptionData() {
            const tbody = document.getElementById('prescriptionsTableBody');
            if (!tbody) return;

            const prescriptions = [
                { id: 'RX001', patient: '22A/UE/BNS/1001', doctor: 'Dr. Smith', medicine: 'Paracetamol', status: 'Pending' },
                { id: 'RX002', patient: '22B/UE/LLB/1002', doctor: 'Dr. Johnson', medicine: 'Amoxicillin', status: 'Dispensed' },
                { id: 'RX003', patient: '22A/UE/BNS/1003', doctor: 'Dr. Brown', medicine: 'Ibuprofen', status: 'Pending' }
            ];

            tbody.innerHTML = prescriptions.map(rx => `
                <tr>
                    <td>${rx.id}</td>
                    <td>${rx.patient}</td>
                    <td>${rx.doctor}</td>
                    <td>${rx.medicine}</td>
                    <td><span class="badge bg-${rx.status === 'Pending' ? 'warning' : 'success'}">${rx.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showAlert('Processing prescription ${rx.id}', 'info')">Process</button>
                    </td>
                </tr>
            `).join('');
        }

        function loadDispensingData() {
            const tbody = document.getElementById('dispensingTableBody');
            if (!tbody) return;

            const dispensingRecords = [
                { time: '09:30', patient: '22A/UE/BNS/1001', medicine: 'Paracetamol', quantity: 2, amount: 100 },
                { time: '10:15', patient: '22B/UE/LLB/1002', medicine: 'Amoxicillin', quantity: 1, amount: 120 },
                { time: '11:00', patient: '22A/UE/BNS/1003', medicine: 'Ibuprofen', quantity: 3, amount: 225 }
            ];

            tbody.innerHTML = dispensingRecords.map(record => `
                <tr>
                    <td>${record.time}</td>
                    <td>${record.patient}</td>
                    <td>${record.medicine}</td>
                    <td>${record.quantity}</td>
                    <td>₦${record.amount}</td>
                </tr>
            `).join('');
        }

        function loadInventoryData() {
            const tbody = document.getElementById('inventoryTableBody');
            if (!tbody) return;

            tbody.innerHTML = Object.values(medicineDatabase).map(med => `
                <tr>
                    <td>${med.name}</td>
                    <td>${med.stock}</td>
                    <td>20</td>
                    <td><span class="badge bg-${med.stock < 20 ? 'danger' : 'success'}">${med.stock < 20 ? 'Low Stock' : 'In Stock'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-warning" onclick="showAlert('Adjusting stock for ${med.name}', 'info')">Adjust</button>
                        <button class="btn btn-sm btn-info" onclick="showAlert('Reordering ${med.name}', 'info')">Reorder</button>
                    </td>
                </tr>
            `).join('');
        }

        function loadSupplierData() {
            const tbody = document.getElementById('suppliersTableBody');
            if (!tbody) return;

            const suppliers = [
                { name: 'GSK Nigeria', contact: '+234 ************', products: 'Paracetamol, Amoxicillin', status: 'Active' },
                { name: 'Emzor Pharmaceuticals', contact: '+234 ************', products: 'Ibuprofen, Insulin', status: 'Active' },
                { name: 'May & Baker', contact: '+234 ************', products: 'Metformin, Aspirin', status: 'Active' }
            ];

            tbody.innerHTML = suppliers.map(supplier => `
                <tr>
                    <td><strong>${supplier.name}</strong></td>
                    <td>${supplier.contact}</td>
                    <td>${supplier.products}</td>
                    <td><span class="badge bg-success">${supplier.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showAlert('Contacting ${supplier.name}', 'info')">Contact</button>
                        <button class="btn btn-sm btn-success" onclick="showAlert('Creating order with ${supplier.name}', 'info')">Order</button>
                    </td>
                </tr>
            `).join('');
        }

        function generateReport() {
            showAlert('Comprehensive pharmacy report generated successfully!', 'success');
        }

        function logout() {
            sessionStorage.removeItem('hospitalUser');
            localStorage.removeItem('hospitalUser');
            window.location.href = 'pharmacy-login.html';
        }

        // Make functions global
        window.showSection = showSection;
        window.showProfileSimple = showProfileSimple;
        window.showSettingsSimple = showSettingsSimple;
        window.dispenseMedicineSimple = dispenseMedicineSimple;
        window.addMedicineSimple = addMedicineSimple;
        window.loadMedicineData = loadMedicineData;
        window.loadPrescriptionData = loadPrescriptionData;
        window.loadDispensingData = loadDispensingData;
        window.loadInventoryData = loadInventoryData;
        window.loadSupplierData = loadSupplierData;
        window.loadReportData = loadReportData;
        window.logout = logout;
    </script>
</body>
</html>
