# 🏥 NEWGATE HOSPITAL MANAGEMENT SYSTEM
## Complete Login Credentials Guide

---

## 🔐 **ADMIN PORTAL CREDENTIALS**

### **System Administrator**
- **Admin ID:** `ADMIN001`
- **Password:** `admin123`
- **Email:** `<EMAIL>`
- **Name:** System Administrator

### **IT Administrator**
- **Admin ID:** `ADMIN002`
- **Password:** `admin456`
- **Email:** `<EMAIL>`
- **Name:** IT Administrator

### **Super Administrator**
- **Admin ID:** `SUPERADMIN`
- **Password:** `super123`
- **Email:** `<EMAIL>`
- **Name:** Super Administrator

---

## 👨‍⚕️ **DOCTOR PORTAL CREDENTIALS**

### **Dr. <PERSON> - Cardiology**
- **Doctor ID:** `DR001`
- **Password:** `doctor123`
- **Email:** `<EMAIL>`
- **Department:** Cardiology
- **Experience:** 10 years

### **Dr. <PERSON> - Pediatrics**
- **Doctor ID:** `DR002`
- **Password:** `doctor456`
- **Email:** `<EMAIL>`
- **Department:** Pediatrics
- **Experience:** 8 years

### **Dr. Michael Brown - General Medicine**
- **Doctor ID:** `DR003`
- **Password:** `doctor789`
- **Email:** `<EMAIL>`
- **Department:** General Medicine
- **Experience:** 12 years

### **Dr. Emily Davis - Orthopedics**
- **Doctor ID:** `DR004`
- **Password:** `doctor321`
- **Email:** `<EMAIL>`
- **Department:** Orthopedics
- **Experience:** 6 years

### **Dr. Robert Wilson - Neurology**
- **Doctor ID:** `DR005`
- **Password:** `doctor654`
- **Email:** `<EMAIL>`
- **Department:** Neurology
- **Experience:** 15 years

---

## 👩‍⚕️ **NURSE PORTAL CREDENTIALS**

### **Nurse Mary Johnson - Emergency**
- **Nurse ID:** `NR001`
- **Password:** `nurse123`
- **Email:** `<EMAIL>`
- **Department:** Emergency
- **Shift:** Day Shift

### **Nurse Patricia Williams - ICU**
- **Nurse ID:** `NR002`
- **Password:** `nurse456`
- **Email:** `<EMAIL>`
- **Department:** ICU
- **Shift:** Night Shift

### **Nurse Jennifer Brown - Pediatrics**
- **Nurse ID:** `NR003`
- **Password:** `nurse789`
- **Email:** `<EMAIL>`
- **Department:** Pediatrics
- **Shift:** Day Shift

### **Nurse Linda Davis - Surgery**
- **Nurse ID:** `NR004`
- **Password:** `nurse321`
- **Email:** `<EMAIL>`
- **Department:** Surgery
- **Shift:** Evening Shift

### **Nurse Barbara Wilson - Maternity**
- **Nurse ID:** `NR005`
- **Password:** `nurse654`
- **Email:** `<EMAIL>`
- **Department:** Maternity
- **Shift:** Day Shift

### **Nurse Susan Miller - Cardiology**
- **Nurse ID:** `NR006`
- **Password:** `nurse987`
- **Email:** `<EMAIL>`
- **Department:** Cardiology
- **Shift:** Night Shift

---

## 🏥 **PATIENT PORTAL CREDENTIALS**

### **John Doe**
- **Patient ID:** `PT001`
- **Password:** `patient123`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1990-05-15

### **Jane Smith**
- **Patient ID:** `PT002`
- **Password:** `patient456`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1985-08-22

### **Michael Johnson**
- **Patient ID:** `PT003`
- **Password:** `patient789`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1992-12-10

### **Sarah Williams**
- **Patient ID:** `PT004`
- **Password:** `patient321`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1988-03-18

### **David Brown**
- **Patient ID:** `PT005`
- **Password:** `patient654`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1995-07-25

### **Emily Davis**
- **Patient ID:** `PT006`
- **Password:** `patient987`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1991-11-08

### **Robert Wilson**
- **Patient ID:** `PT007`
- **Password:** `patient147`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1987-09-14

### **Lisa Anderson**
- **Patient ID:** `PT008`
- **Password:** `patient258`
- **Email:** `<EMAIL>`
- **Phone:** +234-************
- **DOB:** 1993-04-30

---

## 📋 **QUICK REFERENCE**

### **For Testing Admin Features:**
- Use: `ADMIN001` / `admin123`

### **For Testing Doctor Features:**
- Use: `DR001` / `doctor123`

### **For Testing Nurse Features:**
- Use: `NR001` / `nurse123`

### **For Testing Patient Features:**
- Use: `PT001` / `patient123`

---

## 🔒 **Password Reset Information**

For password reset functionality, you'll need:
1. **User ID** (Admin ID, Doctor ID, Nurse ID, or Patient ID)
2. **Registered Email Address**

The system will verify both before sending a reset link.

---

## ⚠️ **Important Notes**

1. **Case Sensitivity:** User IDs are automatically converted to uppercase
2. **Session Management:** Users can choose "Remember Me" for persistent login
3. **Security:** In production, these should be properly hashed and stored securely
4. **Email Verification:** Currently simulated - implement real email service for production

---

## 🚀 **Getting Started**

1. Open `index.html` in your browser
2. Click on the appropriate login portal
3. Use any of the credentials above
4. Explore the dashboard features

**Enjoy testing your Newgate Hospital Management System!** 🏥✨
