// Doctor Login System - Newgate Hospital Management System

// Pre-configured doctor accounts
const doctorAccounts = {
    'DOC001': {
        id: 'DOC001',
        password: 'doctor123',
        name: 'Dr. <PERSON>',
        specialty: 'General Medicine',
        email: '<EMAIL>',
        phone: '+234 ************',
        department: 'General Medicine',
        licenseNumber: 'MD-001-2020',
        experience: '8 years',
        education: 'MBBS, University of Lagos',
        role: 'doctor',
        status: 'active',
        lastLogin: null
    },
    'DOC002': {
        id: 'DOC002',
        password: 'doctor123',
        name: 'Dr. <PERSON><PERSON>',
        specialty: 'Pediatrics',
        email: '<EMAIL>',
        phone: '+234 ************',
        department: 'Pediatrics',
        licenseNumber: 'MD-002-2019',
        experience: '10 years',
        education: 'MBBS, Ahmadu Bello University',
        role: 'doctor',
        status: 'active',
        lastLogin: null
    },
    'DOC003': {
        id: 'DOC003',
        password: 'doctor123',
        name: 'Dr. <PERSON>',
        specialty: 'Cardiology',
        email: '<EMAIL>',
        phone: '+234 ************',
        department: 'Cardiology',
        licenseNumber: 'MD-003-2018',
        experience: '12 years',
        education: 'MBBS, University of Ibadan',
        role: 'doctor',
        status: 'active',
        lastLogin: null
    },
    'DOC004': {
        id: 'DOC004',
        password: 'doctor123',
        name: 'Dr. Aisha Bello',
        specialty: 'Dermatology',
        email: '<EMAIL>',
        phone: '+234 ************',
        department: 'Dermatology',
        licenseNumber: 'MD-004-2021',
        experience: '6 years',
        education: 'MBBS, University of Maiduguri',
        role: 'doctor',
        status: 'active',
        lastLogin: null
    },
    'DOC005': {
        id: 'DOC005',
        password: 'doctor123',
        name: 'Dr. Mohammed Sani',
        specialty: 'Orthopedics',
        email: '<EMAIL>',
        phone: '+234 ************',
        department: 'Orthopedics',
        licenseNumber: 'MD-005-2017',
        experience: '15 years',
        education: 'MBBS, Bayero University',
        role: 'doctor',
        status: 'active',
        lastLogin: null
    }
};

// Updated doctor credentials - use the doctorAccounts above for login

document.addEventListener('DOMContentLoaded', function() {
    initializeDoctorLogin();
});

function initializeDoctorLogin() {
    const loginForm = document.getElementById('doctorLoginForm');
    const togglePasswordBtn = document.getElementById('togglePassword');
    
    // Handle form submission
    if (loginForm) {
        loginForm.addEventListener('submit', handleDoctorLogin);
    }
    
    // Handle password toggle
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            togglePasswordVisibility('loginPassword', 'togglePassword');
        });
    }
    
    // Check if already logged in
    checkExistingSession();

    // Display sample credentials
    displaySampleCredentials();
}

function displaySampleCredentials() {
    const credentialsContainer = document.getElementById('sampleCredentials');
    if (credentialsContainer) {
        credentialsContainer.innerHTML = `
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Sample Doctor Credentials</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Doctor ID:</strong> DOC001<br>
                        <strong>Password:</strong> doctor123<br>
                        <strong>Name:</strong> Dr. Ahmed Musa
                    </div>
                    <div class="col-md-6">
                        <strong>Specialty:</strong> General Medicine<br>
                        <strong>Department:</strong> General Medicine<br>
                        <strong>Status:</strong> Active
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <strong>Other Available Doctors:</strong> DOC002, DOC003, DOC004, DOC005 (all use password: doctor123)
                </small>
            </div>
        `;
    }
}

function handleDoctorLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const doctorId = formData.get('doctorId').trim().toUpperCase();
    const password = formData.get('password').trim();
    const rememberMe = formData.get('rememberMe');
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate API call delay
    setTimeout(() => {
        if (validateDoctorCredentials(doctorId, password)) {
            // Successful login
            const doctorData = doctorAccounts[doctorId];
            doctorData.loginTime = new Date().toISOString();
            
            // Store user data
            const storage = rememberMe ? localStorage : sessionStorage;
            storage.setItem('hospitalUser', JSON.stringify(doctorData));
            
            // Show success message
            showSuccessAlert('Login successful! Redirecting to doctor dashboard...');
            
            // Redirect to doctor dashboard
            setTimeout(() => {
                window.location.href = 'doctor-dashboard.html';
            }, 1500);
            
        } else {
            // Failed login
            showErrorAlert('Invalid Doctor ID or password. Please try again.');
            form.classList.add('was-validated');
        }
        
        hideLoading();
    }, 1000);
}

function validateDoctorCredentials(doctorId, password) {
    return doctorAccounts[doctorId] && doctorAccounts[doctorId].password === password && doctorAccounts[doctorId].status === 'active';
}

function checkExistingSession() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (userData) {
        const user = JSON.parse(userData);
        if (user.role === 'doctor') {
            // User is already logged in as doctor
            showSuccessAlert('You are already logged in. Redirecting...');
            setTimeout(() => {
                window.location.href = 'doctor-dashboard.html';
            }, 1500);
        }
    }
}

function resetPassword() {
    const doctorId = document.getElementById('resetDoctorId').value.trim().toUpperCase();
    const email = document.getElementById('resetEmail').value.trim();
    
    if (!doctorId || !email) {
        showErrorAlert('Please fill in all fields.');
        return;
    }
    
    if (!validateEmail(email)) {
        showErrorAlert('Please enter a valid email address.');
        return;
    }
    
    // Check if doctor exists
    if (!doctorCredentials[doctorId]) {
        showErrorAlert('Doctor ID not found.');
        return;
    }
    
    // Check if email matches
    if (doctorCredentials[doctorId].email !== email) {
        showErrorAlert('Email does not match our records.');
        return;
    }
    
    // Simulate sending reset email
    showSuccessAlert('Password reset link has been sent to your email address.');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
    modal.hide();
    
    // Clear form
    document.getElementById('forgotPasswordForm').reset();
}

// Utility functions
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function showSuccessAlert(message) {
    const alert = document.getElementById('successAlert');
    const messageSpan = document.getElementById('successMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showErrorAlert(message) {
    const alert = document.getElementById('errorAlert');
    const messageSpan = document.getElementById('errorMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Make resetPassword function globally available
window.resetPassword = resetPassword;
