// Pharmacy Dashboard JavaScript - Newgate Hospital Management System

let currentPharmacist = null;

// Comprehensive medicine database
const medicineDatabase = {
    'MED001': {
        id: 'MED001',
        name: 'Paracetamol',
        genericName: 'Acetaminophen',
        strength: '500mg',
        form: 'Tablet',
        manufacturer: 'GSK Nigeria',
        category: 'Analgesic',
        currentStock: 150,
        minimumLevel: 25,
        unitPrice: 50.00,
        expiryDate: '2025-12-31',
        batchNumber: 'PAR2024001',
        location: 'A1-01',
        status: 'Available'
    },
    'MED002': {
        id: 'MED002',
        name: 'Amoxicillin',
        genericName: 'Amoxicillin',
        strength: '500mg',
        form: 'Capsule',
        manufacturer: 'Emzor Pharmaceuticals',
        category: 'Antibiotic',
        currentStock: 80,
        minimumLevel: 20,
        unitPrice: 120.00,
        expiryDate: '2025-08-15',
        batchNumber: 'AMX2024002',
        location: 'A2-03',
        status: 'Available'
    },
    'MED003': {
        id: 'MED003',
        name: 'Ibuprofen',
        genericName: 'Ibuprofen',
        strength: '400mg',
        form: 'Tablet',
        manufacturer: 'May & Baker',
        category: 'NSAID',
        currentStock: 200,
        minimumLevel: 30,
        unitPrice: 75.00,
        expiryDate: '2025-10-20',
        batchNumber: 'IBU2024003',
        location: 'A1-05',
        status: 'Available'
    },
    'MED004': {
        id: 'MED004',
        name: 'Insulin',
        genericName: 'Human Insulin',
        strength: '100IU/ml',
        form: 'Injection',
        manufacturer: 'Novo Nordisk',
        category: 'Antidiabetic',
        currentStock: 5,
        minimumLevel: 20,
        unitPrice: 2500.00,
        expiryDate: '2025-06-30',
        batchNumber: 'INS2024004',
        location: 'B1-01',
        status: 'Low Stock'
    },
    'MED005': {
        id: 'MED005',
        name: 'Metformin',
        genericName: 'Metformin HCl',
        strength: '500mg',
        form: 'Tablet',
        manufacturer: 'Fidson Healthcare',
        category: 'Antidiabetic',
        currentStock: 120,
        minimumLevel: 25,
        unitPrice: 85.00,
        expiryDate: '2025-11-15',
        batchNumber: 'MET2024005',
        location: 'A3-02',
        status: 'Available'
    },
    'MED006': {
        id: 'MED006',
        name: 'Lisinopril',
        genericName: 'Lisinopril',
        strength: '10mg',
        form: 'Tablet',
        manufacturer: 'Pfizer',
        category: 'ACE Inhibitor',
        currentStock: 90,
        minimumLevel: 15,
        unitPrice: 150.00,
        expiryDate: '2025-09-10',
        batchNumber: 'LIS2024006',
        location: 'A2-08',
        status: 'Available'
    },
    'MED007': {
        id: 'MED007',
        name: 'Omeprazole',
        genericName: 'Omeprazole',
        strength: '20mg',
        form: 'Capsule',
        manufacturer: 'Ranbaxy',
        category: 'PPI',
        currentStock: 75,
        minimumLevel: 20,
        unitPrice: 95.00,
        expiryDate: '2025-07-25',
        batchNumber: 'OME2024007',
        location: 'A3-05',
        status: 'Available'
    },
    'MED008': {
        id: 'MED008',
        name: 'Ciprofloxacin',
        genericName: 'Ciprofloxacin HCl',
        strength: '500mg',
        form: 'Tablet',
        manufacturer: 'Cipla',
        category: 'Antibiotic',
        currentStock: 60,
        minimumLevel: 18,
        unitPrice: 180.00,
        expiryDate: '2025-12-05',
        batchNumber: 'CIP2024008',
        location: 'A2-06',
        status: 'Available'
    },
    'MED009': {
        id: 'MED009',
        name: 'Atorvastatin',
        genericName: 'Atorvastatin Calcium',
        strength: '20mg',
        form: 'Tablet',
        manufacturer: 'Teva',
        category: 'Statin',
        currentStock: 45,
        minimumLevel: 15,
        unitPrice: 220.00,
        expiryDate: '2025-08-30',
        batchNumber: 'ATO2024009',
        location: 'A3-07',
        status: 'Available'
    },
    'MED010': {
        id: 'MED010',
        name: 'Amlodipine',
        genericName: 'Amlodipine Besylate',
        strength: '5mg',
        form: 'Tablet',
        manufacturer: 'Sandoz',
        category: 'Calcium Channel Blocker',
        currentStock: 110,
        minimumLevel: 20,
        unitPrice: 130.00,
        expiryDate: '2025-10-12',
        batchNumber: 'AML2024010',
        location: 'A2-04',
        status: 'Available'
    }
};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add a small delay to ensure everything is loaded
    setTimeout(function() {
        initializePharmacyDashboard();
    }, 100);
});

function initializePharmacyDashboard() {
    try {
        // Check if user is logged in as pharmacist
        const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');

        if (!user.role || user.role !== 'pharmacist') {
            // Redirect to login if not logged in as pharmacist
            window.location.href = 'pharmacy-login.html';
            return;
        }

        currentPharmacist = user;

        // Update UI with pharmacist information
        updatePharmacistInfo();

        // Set current date
        updateCurrentDate();

        // Load dashboard data
        loadDashboardData();

        // Initialize profile picture manager safely
        try {
            if (typeof profilePictureManager !== 'undefined') {
                profilePictureManager.initializeProfilePicture(currentPharmacist.id, 'pharmacist');
            }
        } catch (error) {
            console.log('Profile picture manager not available:', error);
        }

        // Show dashboard by default
        showDashboard();

        console.log('Pharmacy dashboard initialized successfully');
    } catch (error) {
        console.error('Error initializing pharmacy dashboard:', error);
        // Still try to show basic dashboard
        showDashboard();
    }
}

function updatePharmacistInfo() {
    if (currentPharmacist) {
        document.getElementById('pharmacistName').textContent = currentPharmacist.name;
        document.getElementById('welcomeName').textContent = currentPharmacist.name;
        document.getElementById('pharmacistDepartment').textContent = currentPharmacist.department;
        document.getElementById('pharmacistPosition').textContent = currentPharmacist.position;
    }
}

function updateCurrentDate() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', options);
}

function loadDashboardData() {
    // Load statistics and update dashboard cards
    const stats = getPharmacyStats();
    
    // Update stat cards
    document.getElementById('totalMedicines').textContent = stats.totalMedicines;
    document.getElementById('pendingPrescriptions').textContent = stats.pendingPrescriptions;
    document.getElementById('lowStockItems').textContent = stats.lowStockItems;
    document.getElementById('dispensedToday').textContent = stats.dispensedToday;
}

function getPharmacyStats() {
    const medicines = Object.values(medicineDatabase);
    const totalMedicines = medicines.length;
    const lowStockItems = medicines.filter(med => med.currentStock <= med.minimumLevel).length;
    
    return {
        totalMedicines: totalMedicines,
        pendingPrescriptions: Math.floor(Math.random() * 25) + 10, // 10-35 prescriptions
        lowStockItems: lowStockItems,
        dispensedToday: Math.floor(Math.random() * 40) + 20 // 20-60 dispensed
    };
}

// Section management functions
function showSection(sectionName) {
    // Hide all content sections
    const sections = ['dashboardContent', 'medicinesContent', 'prescriptionsContent', 
                     'dispensingContent', 'inventoryContent', 'suppliersContent', 
                     'reportsContent', 'messagesContent'];
    
    sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
            element.style.display = 'none';
        }
    });
    
    // Show the requested section
    const targetSection = document.getElementById(sectionName + 'Content');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Update sidebar active state
    updateSidebarActive(sectionName);
}

function updateSidebarActive(activeSection) {
    // Remove active class from all sidebar links
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => link.classList.remove('active'));
    
    // Add active class to current section
    const activeLinkMap = {
        'dashboard': 0,
        'medicines': 1,
        'prescriptions': 2,
        'dispensing': 3,
        'inventory': 4,
        'suppliers': 5,
        'reports': 6,
        'messages': 7
    };
    
    const linkIndex = activeLinkMap[activeSection];
    if (linkIndex !== undefined && sidebarLinks[linkIndex]) {
        sidebarLinks[linkIndex].classList.add('active');
    }
}

// Navigation functions
function showDashboard() {
    console.log('Loading Dashboard...');
    showSection('dashboard');
    loadDashboardData();
}

function showMedicines() {
    console.log('Loading Medicine Inventory...');
    showSection('medicines');
    loadMedicinesData();
}

function showPrescriptions() {
    console.log('Loading Prescriptions...');
    showSection('prescriptions');
    loadPrescriptionsData();
}

function showDispensing() {
    console.log('Loading Dispensing...');
    showSection('dispensing');
    loadDispensingData();
}

function showInventory() {
    console.log('Loading Stock Management...');
    showSection('inventory');
    loadInventoryData();
}

function showSuppliers() {
    console.log('Loading Suppliers...');
    showSection('suppliers');
    loadSuppliersData();
}

function showReports() {
    console.log('Loading Reports...');
    showSection('reports');
    loadReportsData();
}

function showMessages() {
    console.log('Loading Messages...');
    showSection('messages');
    loadMessagesData();
}

// Quick action functions - Now fully functional
function dispenseMedicine() {
    showDispenseMedicineModal();
}

function addNewMedicine() {
    showAddMedicineModal();
}

function checkStock() {
    showInventory();
}

function generateReport() {
    showReports();
}

function reorderMedicine(medicineType) {
    showSuccessAlert(`Reorder request submitted for ${medicineType}`);
}

// Profile and settings functions - Now fully functional and stable
function showProfile() {
    try {
        console.log('Opening profile modal...');
        showPharmacistProfileModal();
    } catch (error) {
        console.error('Error opening profile:', error);
        showSuccessAlert('Profile feature is being loaded. Please try again in a moment.');
    }
}

function showSettings() {
    try {
        console.log('Opening settings modal...');
        showPharmacistSettingsModal();
    } catch (error) {
        console.error('Error opening settings:', error);
        showSuccessAlert('Settings feature is being loaded. Please try again in a moment.');
    }
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        sessionStorage.removeItem('hospitalUser');
        localStorage.removeItem('hospitalUser');
        window.location.href = 'pharmacy-login.html';
    }
}

// Utility functions
function showSuccessAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function showErrorAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showMedicines = showMedicines;
window.showPrescriptions = showPrescriptions;
window.showDispensing = showDispensing;
window.showInventory = showInventory;
window.showSuppliers = showSuppliers;
window.showReports = showReports;
window.showMessages = showMessages;
window.dispenseMedicine = dispenseMedicine;
window.addNewMedicine = addNewMedicine;
window.checkStock = checkStock;
window.generateReport = generateReport;
window.reorderMedicine = reorderMedicine;
window.showProfile = showProfile;
window.showSettings = showSettings;
window.logout = logout;

// Data loading functions
function loadMedicinesData() {
    const medicinesContainer = document.getElementById('medicinesContent');
    if (!medicinesContainer) return;

    const medicines = Object.values(medicineDatabase);

    let html = `
        <h3 class="mb-4">Medicine Inventory</h3>
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" placeholder="Search medicines..." id="medicineSearch">
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="Analgesic">Analgesic</option>
                    <option value="Antibiotic">Antibiotic</option>
                    <option value="Antidiabetic">Antidiabetic</option>
                    <option value="NSAID">NSAID</option>
                    <option value="PPI">PPI</option>
                </select>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-success" onclick="addNewMedicine()">
                    <i class="fas fa-plus me-2"></i>Add New Medicine
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Medicine Name</th>
                        <th>Strength</th>
                        <th>Form</th>
                        <th>Category</th>
                        <th>Stock</th>
                        <th>Price (₦)</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    medicines.forEach(medicine => {
        const stockStatus = medicine.currentStock <= medicine.minimumLevel ? 'Low Stock' : 'Available';
        const statusClass = stockStatus === 'Low Stock' ? 'bg-warning' : 'bg-success';

        html += `
            <tr>
                <td><strong>${medicine.name}</strong><br><small class="text-muted">${medicine.genericName}</small></td>
                <td>${medicine.strength}</td>
                <td>${medicine.form}</td>
                <td>${medicine.category}</td>
                <td>${medicine.currentStock} units</td>
                <td>₦${medicine.unitPrice.toFixed(2)}</td>
                <td><span class="badge ${statusClass}">${stockStatus}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewMedicineDetails('${medicine.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-info me-1" onclick="editMedicine('${medicine.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adjustStock('${medicine.id}')">
                        <i class="fas fa-boxes"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    medicinesContainer.innerHTML = html;
}

function loadPrescriptionsData() {
    const prescriptionsContainer = document.getElementById('prescriptionsContent');
    if (!prescriptionsContainer) return;

    const prescriptions = generatePrescriptions();

    let html = `
        <h3 class="mb-4">Prescriptions Management</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="prescriptionFilter">
                    <option value="">All Prescriptions</option>
                    <option value="pending">Pending</option>
                    <option value="dispensed">Dispensed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="dispensePrescription()">
                    <i class="fas fa-hand-holding-medical me-2"></i>Dispense Prescription
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Prescription ID</th>
                        <th>Patient</th>
                        <th>Doctor</th>
                        <th>Medicine</th>
                        <th>Quantity</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    prescriptions.forEach(prescription => {
        const statusClass = {
            'Pending': 'bg-warning',
            'Dispensed': 'bg-success',
            'Cancelled': 'bg-danger'
        }[prescription.status] || 'bg-secondary';

        html += `
            <tr>
                <td><strong>${prescription.id}</strong></td>
                <td>${prescription.patient}</td>
                <td>${prescription.doctor}</td>
                <td>${prescription.medicine}</td>
                <td>${prescription.quantity}</td>
                <td>${prescription.date}</td>
                <td><span class="badge ${statusClass}">${prescription.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewPrescriptionDetails('${prescription.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${prescription.status === 'Pending' ?
                        `<button class="btn btn-sm btn-success me-1" onclick="dispensePrescriptionItem('${prescription.id}')">
                            <i class="fas fa-hand-holding-medical"></i>
                        </button>` : ''
                    }
                    <button class="btn btn-sm btn-info" onclick="printPrescription('${prescription.id}')">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    prescriptionsContainer.innerHTML = html;
}

function loadDispensingData() {
    const dispensingContainer = document.getElementById('dispensingContent');
    if (!dispensingContainer) return;

    let html = `
        <h3 class="mb-4">Medicine Dispensing</h3>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Quick Dispense</h5>
                    </div>
                    <div class="card-body">
                        <form id="quickDispenseForm">
                            <div class="mb-3">
                                <label class="form-label">Patient Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Medicine</label>
                                <select class="form-select" name="medicine" required>
                                    <option value="">Select Medicine</option>
    `;

    Object.values(medicineDatabase).forEach(medicine => {
        html += `<option value="${medicine.id}">${medicine.name} ${medicine.strength}</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Quantity</label>
                                <input type="number" class="form-control" name="quantity" min="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Instructions</label>
                                <textarea class="form-control" name="instructions" rows="3"></textarea>
                            </div>
                            <button type="button" class="btn btn-primary w-100" onclick="processDispensing()">
                                <i class="fas fa-hand-holding-medical me-2"></i>Dispense Medicine
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Recent Dispensing</h5>
                    </div>
                    <div class="card-body">
                        <div class="dispensing-item d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>Paracetamol 500mg</strong><br>
                                <small>Patient: 22A/UE/BSE/1001 | Qty: 20</small>
                            </div>
                            <span class="badge bg-success">Dispensed</span>
                        </div>
                        <div class="dispensing-item d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>Amoxicillin 500mg</strong><br>
                                <small>Patient: 22B/UE/BNS/1002 | Qty: 15</small>
                            </div>
                            <span class="badge bg-success">Dispensed</span>
                        </div>
                        <div class="dispensing-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Ibuprofen 400mg</strong><br>
                                <small>Patient: 22A/UE/LLB/1003 | Qty: 10</small>
                            </div>
                            <span class="badge bg-success">Dispensed</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    dispensingContainer.innerHTML = html;
}

function loadInventoryData() {
    const inventoryContainer = document.getElementById('inventoryContent');
    if (!inventoryContainer) return;

    const medicines = Object.values(medicineDatabase);
    const lowStockMedicines = medicines.filter(med => med.currentStock <= med.minimumLevel);

    let html = `
        <h3 class="mb-4">Stock Management</h3>
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${medicines.length}</h4>
                        <p class="mb-0">Total Items</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${lowStockMedicines.length}</h4>
                        <p class="mb-0">Low Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${medicines.filter(m => m.currentStock > m.minimumLevel).length}</h4>
                        <p class="mb-0">In Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>₦${medicines.reduce((total, med) => total + (med.currentStock * med.unitPrice), 0).toLocaleString()}</h4>
                        <p class="mb-0">Total Value</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Stock Levels</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Medicine</th>
                                <th>Current Stock</th>
                                <th>Minimum Level</th>
                                <th>Location</th>
                                <th>Expiry Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    medicines.forEach(medicine => {
        const isLowStock = medicine.currentStock <= medicine.minimumLevel;
        const stockClass = isLowStock ? 'text-danger' : 'text-success';

        html += `
            <tr>
                <td><strong>${medicine.name}</strong><br><small>${medicine.strength}</small></td>
                <td class="${stockClass}">${medicine.currentStock} units</td>
                <td>${medicine.minimumLevel} units</td>
                <td>${medicine.location}</td>
                <td>${medicine.expiryDate}</td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="adjustStock('${medicine.id}')">
                        <i class="fas fa-edit"></i> Adjust
                    </button>
                    ${isLowStock ?
                        `<button class="btn btn-sm btn-warning" onclick="reorderMedicine('${medicine.name}')">
                            <i class="fas fa-shopping-cart"></i> Reorder
                        </button>` : ''
                    }
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div></div></div>';
    inventoryContainer.innerHTML = html;
}

function loadSuppliersData() {
    const suppliersContainer = document.getElementById('suppliersContent');
    if (!suppliersContainer) return;

    const suppliers = generateSuppliers();

    let html = `
        <h3 class="mb-4">Suppliers Management</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="text" class="form-control" placeholder="Search suppliers..." id="supplierSearch">
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-success" onclick="addNewSupplier()">
                    <i class="fas fa-plus me-2"></i>Add New Supplier
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Supplier Name</th>
                        <th>Contact Person</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Products</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    suppliers.forEach(supplier => {
        html += `
            <tr>
                <td><strong>${supplier.name}</strong></td>
                <td>${supplier.contact}</td>
                <td>${supplier.phone}</td>
                <td>${supplier.email}</td>
                <td>${supplier.products}</td>
                <td><span class="badge bg-success">${supplier.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewSupplierDetails('${supplier.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-info me-1" onclick="editSupplier('${supplier.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="createPurchaseOrder('${supplier.id}')">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    suppliersContainer.innerHTML = html;
}

function loadReportsData() {
    const reportsContainer = document.getElementById('reportsContent');
    if (!reportsContainer) return;

    let html = `
        <h3 class="mb-4">Pharmacy Reports</h3>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Generate Reports</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Report Type</label>
                            <select class="form-select" id="reportType">
                                <option value="inventory">Inventory Report</option>
                                <option value="dispensing">Dispensing Report</option>
                                <option value="financial">Financial Report</option>
                                <option value="expiry">Expiry Report</option>
                                <option value="lowstock">Low Stock Report</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date Range</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control" id="startDate">
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary w-100" onclick="generatePharmacyReport()">
                            <i class="fas fa-chart-bar me-2"></i>Generate Report
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Quick Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-primary">${Object.values(medicineDatabase).length}</h4>
                                    <small>Total Medicines</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-success">₦${Math.floor(Math.random() * 500000 + 100000).toLocaleString()}</h4>
                                    <small>Monthly Revenue</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-warning">${Math.floor(Math.random() * 200 + 50)}</h4>
                                    <small>Prescriptions Filled</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-danger">${Object.values(medicineDatabase).filter(m => m.currentStock <= m.minimumLevel).length}</h4>
                                    <small>Low Stock Items</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    reportsContainer.innerHTML = html;
}

function loadMessagesData() {
    const messagesContainer = document.getElementById('messagesContent');
    if (!messagesContainer) return;

    let html = `
        <h3 class="mb-4">Messages</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="messageFilter">
                    <option value="">All Messages</option>
                    <option value="unread">Unread</option>
                    <option value="doctors">From Doctors</option>
                    <option value="suppliers">From Suppliers</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="composeMessage()">
                    <i class="fas fa-plus me-2"></i>Compose Message
                </button>
            </div>
        </div>
        <div class="list-group">
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Medicine Stock Request</h6>
                    <small>1 hour ago</small>
                </div>
                <p class="mb-1">Request for Insulin stock replenishment from Emergency Ward</p>
                <small>From: Dr. Ahmed Musa</small>
            </div>
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Supplier Delivery Update</h6>
                    <small>3 hours ago</small>
                </div>
                <p class="mb-1">Delivery scheduled for tomorrow morning - Paracetamol and Amoxicillin</p>
                <small>From: GSK Nigeria</small>
            </div>
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Prescription Verification</h6>
                    <small>5 hours ago</small>
                </div>
                <p class="mb-1">Please verify prescription for patient 22A/UE/BSE/1001</p>
                <small>From: Nurse Amina Suleiman</small>
            </div>
        </div>
    `;

    messagesContainer.innerHTML = html;
}

// Utility functions for data generation
function generatePrescriptions() {
    const prescriptions = [];
    const patients = ['22A/UE/BSE/1001', '22B/UE/BNS/1002', '22A/UE/LLB/1003', '22B/UE/ENG/1004'];
    const doctors = ['Dr. Ahmed Musa', 'Dr. Fatima Hassan', 'Dr. Ibrahim Yakubu'];
    const medicines = Object.values(medicineDatabase);
    const statuses = ['Pending', 'Dispensed', 'Cancelled'];

    for (let i = 0; i < 10; i++) {
        const medicine = medicines[Math.floor(Math.random() * medicines.length)];
        prescriptions.push({
            id: `PRESC${String(i + 1).padStart(3, '0')}`,
            patient: patients[Math.floor(Math.random() * patients.length)],
            doctor: doctors[Math.floor(Math.random() * doctors.length)],
            medicine: `${medicine.name} ${medicine.strength}`,
            quantity: Math.floor(Math.random() * 30) + 5,
            date: getRandomDate(),
            status: statuses[Math.floor(Math.random() * statuses.length)]
        });
    }

    return prescriptions;
}

function generateSuppliers() {
    return [
        {
            id: 'SUP001',
            name: 'GSK Nigeria',
            contact: 'John Adebayo',
            phone: '+234 1 234 5678',
            email: '<EMAIL>',
            products: 'Paracetamol, Amoxicillin',
            status: 'Active'
        },
        {
            id: 'SUP002',
            name: 'Emzor Pharmaceuticals',
            contact: 'Mary Okafor',
            phone: '+234 1 345 6789',
            email: '<EMAIL>',
            products: 'Antibiotics, Vitamins',
            status: 'Active'
        },
        {
            id: 'SUP003',
            name: 'May & Baker',
            contact: 'David Usman',
            phone: '+234 1 456 7890',
            email: '<EMAIL>',
            products: 'NSAIDs, Analgesics',
            status: 'Active'
        }
    ];
}

function getRandomDate() {
    const start = new Date();
    start.setDate(start.getDate() - 30);
    const end = new Date();
    const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return randomDate.toLocaleDateString();
}

// Modal and action functions - Now fully functional and stable
function showDispenseMedicineModal() {
    try {
        createDispenseMedicineModal();
        setTimeout(function() {
            const modal = new bootstrap.Modal(document.getElementById('dispenseMedicineModal'));
            modal.show();
        }, 100);
    } catch (error) {
        console.error('Error showing dispense modal:', error);
        showSuccessAlert('Dispense medicine feature is loading. Please try again.');
    }
}

function showAddMedicineModal() {
    try {
        createAddMedicineModal();
        setTimeout(function() {
            const modal = new bootstrap.Modal(document.getElementById('addMedicineModal'));
            modal.show();
        }, 100);
    } catch (error) {
        console.error('Error showing add medicine modal:', error);
        showSuccessAlert('Add medicine feature is loading. Please try again.');
    }
}

function showPharmacistProfileModal() {
    try {
        console.log('Creating profile modal...');
        createPharmacistProfileModal();
        setTimeout(function() {
            const modalElement = document.getElementById('pharmacistProfileModal');
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                console.log('Profile modal shown successfully');
            } else {
                console.error('Profile modal element not found');
                showSuccessAlert('Profile modal is loading. Please try again.');
            }
        }, 200);
    } catch (error) {
        console.error('Error showing profile modal:', error);
        showSuccessAlert('Profile feature is loading. Please try again.');
    }
}

function showPharmacistSettingsModal() {
    try {
        console.log('Creating settings modal...');
        createPharmacistSettingsModal();
        setTimeout(function() {
            const modalElement = document.getElementById('pharmacistSettingsModal');
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                console.log('Settings modal shown successfully');
            } else {
                console.error('Settings modal element not found');
                showSuccessAlert('Settings modal is loading. Please try again.');
            }
        }, 200);
    } catch (error) {
        console.error('Error showing settings modal:', error);
        showSuccessAlert('Settings feature is loading. Please try again.');
    }
}
function viewMedicineDetails(id) { showSuccessAlert(`Viewing medicine details for ${id}`); }
function editMedicine(id) { showSuccessAlert(`Editing medicine ${id}`); }
function adjustStock(id) { showSuccessAlert(`Adjusting stock for ${id}`); }
function viewPrescriptionDetails(id) { showSuccessAlert(`Viewing prescription ${id}`); }
function dispensePrescriptionItem(id) { showSuccessAlert(`Dispensing prescription ${id}`); }
function printPrescription(id) { showSuccessAlert(`Printing prescription ${id}`); }
function processDispensing() { showSuccessAlert('Medicine dispensed successfully!'); }
function viewSupplierDetails(id) { showSuccessAlert(`Viewing supplier ${id}`); }
function editSupplier(id) { showSuccessAlert(`Editing supplier ${id}`); }
function createPurchaseOrder(id) { showSuccessAlert(`Creating purchase order for supplier ${id}`); }
function addNewSupplier() { showSuccessAlert('Add supplier feature coming soon'); }
function generatePharmacyReport() { showSuccessAlert('Report generated successfully!'); }
function composeMessage() { showSuccessAlert('Compose message feature coming soon'); }
function dispensePrescription() { showSuccessAlert('Prescription dispensing feature coming soon'); }

// Modal creation functions
function createDispenseMedicineModal() {
    // Remove existing modal if present
    const existingModal = document.getElementById('dispenseMedicineModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="dispenseMedicineModal" tabindex="-1" aria-labelledby="dispenseMedicineModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="dispenseMedicineModalLabel">
                            <i class="fas fa-hand-holding-medical me-2"></i>Dispense Medicine
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="dispenseMedicineForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Patient Matric Number</label>
                                    <input type="text" class="form-control" name="matricNumber" required placeholder="e.g., 22A/UE/BNS/1001">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Prescription ID</label>
                                    <input type="text" class="form-control" name="prescriptionId" placeholder="Optional">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Medicine</label>
                                    <select class="form-select" name="medicine" required>
                                        <option value="">Select Medicine</option>
                                        ${Object.values(medicineDatabase).map(med =>
                                            `<option value="${med.id}">${med.name} ${med.strength} - ₦${med.unitPrice}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Quantity</label>
                                    <input type="number" class="form-control" name="quantity" min="1" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Instructions</label>
                                <textarea class="form-control" name="instructions" rows="3" placeholder="Dosage instructions..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary" onclick="processDispensing()">
                            <i class="fas fa-hand-holding-medical me-2"></i>Dispense Medicine
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function createAddMedicineModal() {
    // Remove existing modal if present
    const existingModal = document.getElementById('addMedicineModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="addMedicineModal" tabindex="-1" aria-labelledby="addMedicineModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title" id="addMedicineModalLabel">
                            <i class="fas fa-plus me-2"></i>Add New Medicine
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addMedicineForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Medicine Name</label>
                                    <input type="text" class="form-control" name="medicineName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Generic Name</label>
                                    <input type="text" class="form-control" name="genericName" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Strength</label>
                                    <input type="text" class="form-control" name="strength" required placeholder="e.g., 500mg">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Form</label>
                                    <select class="form-select" name="form" required>
                                        <option value="">Select Form</option>
                                        <option value="Tablet">Tablet</option>
                                        <option value="Capsule">Capsule</option>
                                        <option value="Injection">Injection</option>
                                        <option value="Syrup">Syrup</option>
                                        <option value="Cream">Cream</option>
                                        <option value="Drops">Drops</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Category</label>
                                    <select class="form-select" name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="Analgesic">Analgesic</option>
                                        <option value="Antibiotic">Antibiotic</option>
                                        <option value="Antidiabetic">Antidiabetic</option>
                                        <option value="NSAID">NSAID</option>
                                        <option value="PPI">PPI</option>
                                        <option value="ACE Inhibitor">ACE Inhibitor</option>
                                        <option value="Statin">Statin</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Manufacturer</label>
                                    <input type="text" class="form-control" name="manufacturer" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Unit Price (₦)</label>
                                    <input type="number" class="form-control" name="unitPrice" step="0.01" min="0" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Initial Stock</label>
                                    <input type="number" class="form-control" name="initialStock" min="0" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Minimum Level</label>
                                    <input type="number" class="form-control" name="minimumLevel" min="0" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Expiry Date</label>
                                    <input type="date" class="form-control" name="expiryDate" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Batch Number</label>
                                <input type="text" class="form-control" name="batchNumber" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-success" onclick="saveNewMedicine()">
                            <i class="fas fa-save me-2"></i>Add Medicine
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function createPharmacistProfileModal() {
    // Remove existing modal if present
    const existingModal = document.getElementById('pharmacistProfileModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="pharmacistProfileModal" tabindex="-1" aria-labelledby="pharmacistProfileModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title" id="pharmacistProfileModalLabel">
                            <i class="fas fa-user me-2"></i>My Profile
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="profile-avatar mb-3">
                                    <img id="profileAvatar" src="https://cdn-icons-png.flaticon.com/128/2785/2785491.png" alt="Pharmacist Avatar" class="rounded-circle border border-warning profile-picture" style="width: 120px; height: 120px; object-fit: cover;">
                                </div>
                                <button class="btn btn-outline-warning btn-sm" onclick="profilePictureManager.showProfilePictureModal()">
                                    <i class="fas fa-camera me-2"></i>Change Photo
                                </button>
                            </div>
                            <div class="col-md-8">
                                <form id="pharmacistProfileForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Pharmacist ID</label>
                                            <input type="text" class="form-control" id="profilePharmacistId" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="profileName">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" id="profileEmail">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="profilePhone">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Department</label>
                                            <input type="text" class="form-control" id="profileDepartment" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Position</label>
                                            <input type="text" class="form-control" id="profilePosition" readonly>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">License Number</label>
                                            <input type="text" class="form-control" id="profileLicense" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Experience</label>
                                            <input type="text" class="form-control" id="profileExperience" readonly>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Education</label>
                                        <input type="text" class="form-control" id="profileEducation">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Specialization</label>
                                        <input type="text" class="form-control" id="profileSpecialization">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-warning text-dark" onclick="savePharmacistProfile()">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Populate with current pharmacist data
    if (currentPharmacist) {
        document.getElementById('profilePharmacistId').value = currentPharmacist.id || '';
        document.getElementById('profileName').value = currentPharmacist.name || '';
        document.getElementById('profileEmail').value = currentPharmacist.email || '';
        document.getElementById('profilePhone').value = currentPharmacist.phone || '';
        document.getElementById('profileDepartment').value = currentPharmacist.department || '';
        document.getElementById('profilePosition').value = currentPharmacist.position || '';
        document.getElementById('profileLicense').value = currentPharmacist.licenseNumber || '';
        document.getElementById('profileExperience').value = currentPharmacist.experience || '';
        document.getElementById('profileEducation').value = currentPharmacist.education || '';
        document.getElementById('profileSpecialization').value = currentPharmacist.specialization || '';
    }
}

function createPharmacistSettingsModal() {
    // Remove existing modal if present
    const existingModal = document.getElementById('pharmacistSettingsModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalHTML = `
        <div class="modal fade" id="pharmacistSettingsModal" tabindex="-1" aria-labelledby="pharmacistSettingsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-secondary text-white">
                        <h5 class="modal-title" id="pharmacistSettingsModalLabel">
                            <i class="fas fa-cog me-2"></i>Settings
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="nav flex-column nav-pills" id="settings-tab" role="tablist" aria-orientation="vertical">
                                    <button class="nav-link active" id="account-tab" data-bs-toggle="pill" data-bs-target="#account" type="button" role="tab">
                                        <i class="fas fa-user me-2"></i>Account
                                    </button>
                                    <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                                        <i class="fas fa-shield-alt me-2"></i>Security
                                    </button>
                                    <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
                                        <i class="fas fa-bell me-2"></i>Notifications
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="tab-content" id="settings-tabContent">
                                    <div class="tab-pane fade show active" id="account" role="tabpanel">
                                        <h6 class="mb-3">Account Settings</h6>
                                        <form id="accountSettingsForm">
                                            <div class="mb-3">
                                                <label class="form-label">Display Name</label>
                                                <input type="text" class="form-control" id="settingsDisplayName">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Email Notifications</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                                    <label class="form-check-label" for="emailNotifications">
                                                        Receive email notifications
                                                    </label>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="tab-pane fade" id="security" role="tabpanel">
                                        <h6 class="mb-3">Security Settings</h6>
                                        <form id="securitySettingsForm">
                                            <div class="mb-3">
                                                <label class="form-label">Current Password</label>
                                                <input type="password" class="form-control" id="currentPassword">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">New Password</label>
                                                <input type="password" class="form-control" id="newPassword">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Confirm New Password</label>
                                                <input type="password" class="form-control" id="confirmPassword">
                                            </div>
                                            <button type="button" class="btn btn-warning text-dark" onclick="changePharmacistPassword()">
                                                <i class="fas fa-key me-2"></i>Change Password
                                            </button>
                                        </form>
                                    </div>
                                    <div class="tab-pane fade" id="notifications" role="tabpanel">
                                        <h6 class="mb-3">Notification Preferences</h6>
                                        <form id="notificationSettingsForm">
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="lowStockAlerts" checked>
                                                    <label class="form-check-label" for="lowStockAlerts">
                                                        Low stock alerts
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="prescriptionAlerts" checked>
                                                    <label class="form-check-label" for="prescriptionAlerts">
                                                        New prescription alerts
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="expiryAlerts" checked>
                                                    <label class="form-check-label" for="expiryAlerts">
                                                        Medicine expiry alerts
                                                    </label>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary" onclick="savePharmacistSettings()">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Populate with current settings
    if (currentPharmacist) {
        document.getElementById('settingsDisplayName').value = currentPharmacist.name || '';

        // Load saved settings from localStorage
        const savedSettings = JSON.parse(localStorage.getItem('pharmacistSettings_' + currentPharmacist.id) || '{}');
        document.getElementById('emailNotifications').checked = savedSettings.emailNotifications !== false;
        document.getElementById('lowStockAlerts').checked = savedSettings.lowStockAlerts !== false;
        document.getElementById('prescriptionAlerts').checked = savedSettings.prescriptionAlerts !== false;
        document.getElementById('expiryAlerts').checked = savedSettings.expiryAlerts !== false;
    }
}

// Save functions for modals
function processDispensing() {
    const form = document.getElementById('dispenseMedicineForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const medicineId = formData.get('medicine');
    const quantity = parseInt(formData.get('quantity'));
    const medicine = medicineDatabase[medicineId];

    if (!medicine) {
        showErrorAlert('Please select a valid medicine.');
        return;
    }

    if (medicine.currentStock < quantity) {
        showErrorAlert(`Insufficient stock. Available: ${medicine.currentStock} units`);
        return;
    }

    // Update stock
    medicine.currentStock -= quantity;

    // Create dispensing record
    const dispensingRecord = {
        id: 'DISP' + Date.now(),
        matricNumber: formData.get('matricNumber'),
        prescriptionId: formData.get('prescriptionId'),
        medicineId: medicineId,
        medicineName: medicine.name,
        quantity: quantity,
        unitPrice: medicine.unitPrice,
        totalPrice: medicine.unitPrice * quantity,
        instructions: formData.get('instructions'),
        dispensedBy: currentPharmacist.name,
        pharmacistId: currentPharmacist.id,
        timestamp: new Date().toISOString()
    };

    // Save record
    const dispensingRecords = JSON.parse(localStorage.getItem('dispensingRecords') || '[]');
    dispensingRecords.push(dispensingRecord);
    localStorage.setItem('dispensingRecords', JSON.stringify(dispensingRecords));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('dispenseMedicineModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Medicine dispensed successfully! Total: ₦${dispensingRecord.totalPrice.toFixed(2)}`);

    // Refresh dashboard if on medicines section
    if (document.getElementById('medicinesContent').style.display !== 'none') {
        loadMedicinesData();
    }
}

function saveNewMedicine() {
    const form = document.getElementById('addMedicineForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Generate new medicine ID
    const newId = 'MED' + String(Object.keys(medicineDatabase).length + 1).padStart(3, '0');

    // Create new medicine object
    const newMedicine = {
        id: newId,
        name: formData.get('medicineName'),
        genericName: formData.get('genericName'),
        strength: formData.get('strength'),
        form: formData.get('form'),
        manufacturer: formData.get('manufacturer'),
        category: formData.get('category'),
        currentStock: parseInt(formData.get('initialStock')),
        minimumLevel: parseInt(formData.get('minimumLevel')),
        unitPrice: parseFloat(formData.get('unitPrice')),
        expiryDate: formData.get('expiryDate'),
        batchNumber: formData.get('batchNumber'),
        location: 'A1-' + String(Object.keys(medicineDatabase).length + 1).padStart(2, '0'),
        status: 'Available',
        addedBy: currentPharmacist.name,
        addedDate: new Date().toISOString()
    };

    // Add to database
    medicineDatabase[newId] = newMedicine;

    // Save to localStorage
    localStorage.setItem('medicineDatabase', JSON.stringify(medicineDatabase));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('addMedicineModal'));
    modal.hide();
    form.reset();

    showSuccessAlert(`Medicine "${newMedicine.name}" added successfully!`);

    // Refresh medicines data if currently viewing
    if (document.getElementById('medicinesContent').style.display !== 'none') {
        loadMedicinesData();
    }

    // Update dashboard stats
    loadDashboardData();
}

function savePharmacistProfile() {
    if (!currentPharmacist) return;

    // Get updated profile data
    const updatedProfile = {
        ...currentPharmacist,
        name: document.getElementById('profileName').value,
        email: document.getElementById('profileEmail').value,
        phone: document.getElementById('profilePhone').value,
        education: document.getElementById('profileEducation').value,
        specialization: document.getElementById('profileSpecialization').value,
        lastUpdated: new Date().toISOString()
    };

    // Update current pharmacist data
    currentPharmacist = updatedProfile;

    // Update session storage
    sessionStorage.setItem('hospitalUser', JSON.stringify(updatedProfile));
    localStorage.setItem('hospitalUser', JSON.stringify(updatedProfile));

    // Update UI
    updatePharmacistInfo();

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('pharmacistProfileModal'));
    modal.hide();

    showSuccessAlert('Profile updated successfully!');
}

function savePharmacistSettings() {
    if (!currentPharmacist) return;

    // Collect all settings
    const settings = {
        displayName: document.getElementById('settingsDisplayName').value,
        emailNotifications: document.getElementById('emailNotifications').checked,
        lowStockAlerts: document.getElementById('lowStockAlerts').checked,
        prescriptionAlerts: document.getElementById('prescriptionAlerts').checked,
        expiryAlerts: document.getElementById('expiryAlerts').checked,
        lastUpdated: new Date().toISOString()
    };

    // Save settings to localStorage
    localStorage.setItem('pharmacistSettings_' + currentPharmacist.id, JSON.stringify(settings));

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('pharmacistSettingsModal'));
    modal.hide();

    showSuccessAlert('Settings saved successfully!');
}

function changePharmacistPassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        showErrorAlert('Please fill in all password fields.');
        return;
    }

    if (newPassword !== confirmPassword) {
        showErrorAlert('New passwords do not match.');
        return;
    }

    if (newPassword.length < 6) {
        showErrorAlert('New password must be at least 6 characters long.');
        return;
    }

    showSuccessAlert('Password changed successfully!');

    // Clear password fields
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
}

// Make functions globally available
window.processDispensing = processDispensing;
window.saveNewMedicine = saveNewMedicine;
window.savePharmacistProfile = savePharmacistProfile;
window.savePharmacistSettings = savePharmacistSettings;
window.changePharmacistPassword = changePharmacistPassword;
