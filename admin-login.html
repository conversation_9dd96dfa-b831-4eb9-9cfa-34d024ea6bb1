<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index-redesigned.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index-redesigned.html">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="loginDropdown" role="button" data-bs-toggle="dropdown">
                            Other Logins
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="patient-login.html"><i class="fas fa-user me-2"></i>Patient Login</a></li>
                            <li><a class="dropdown-item" href="doctor-login.html"><i class="fas fa-user-md me-2"></i>Doctor Login</a></li>
                            <li><a class="dropdown-item" href="nurse-login.html"><i class="fas fa-user-nurse me-2"></i>Nurse Login</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Login Section -->
    <section class="py-5 mt-5">
        <div class="container">
            <div class="row justify-content-center align-items-center min-vh-100">
                <div class="col-lg-5 col-md-7">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-danger text-white text-center py-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-shield me-2"></i>
                                Admin Login
                            </h2>
                            <p class="mb-0 mt-2">System Administration Portal</p>
                        </div>
                        <div class="card-body p-5">
                            <form id="adminLoginForm" novalidate>
                                <div class="mb-4">
                                    <label for="adminId" class="form-label">Admin ID *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-id-badge text-danger"></i>
                                        </span>
                                        <input type="text" class="form-control" id="adminId" name="adminId" required placeholder="Enter your Admin ID">
                                    </div>
                                    <div class="invalid-feedback">Please provide your Admin ID.</div>
                                </div>

                                <div class="mb-4">
                                    <label for="loginPassword" class="form-label">Password *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock text-danger"></i>
                                        </span>
                                        <input type="password" class="form-control" id="loginPassword" name="loginPassword" required placeholder="Enter your password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">Please provide your password.</div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                                        <label class="form-check-label" for="rememberMe">
                                            Remember me
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mb-4">
                                    <button type="submit" class="btn btn-danger btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </button>
                                </div>

                                <div class="text-center">
                                    <a href="#" class="text-danger text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                        <i class="fas fa-key me-1"></i>Forgot Password?
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="resetAdminId" class="form-label">Admin ID</label>
                            <input type="text" class="form-control" id="resetAdminId" required placeholder="Enter your Admin ID">
                        </div>
                        <div class="mb-3">
                            <label for="resetEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="resetEmail" required placeholder="Enter your registered email">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="resetPassword()">Send Reset Link</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Alert -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="successAlert" class="alert alert-success alert-dismissible fade" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span id="successMessage">Login successful!</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>

    <!-- Error Alert -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="errorAlert" class="alert alert-danger alert-dismissible fade" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span id="errorMessage">Login failed!</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Final Year Project - Newgate University Minna</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/admin-login.js"></script>
</body>
</html>
