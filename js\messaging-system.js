// Universal Messaging System for Newgate Hospital
// Allows communication between all modules: doctors, nurses, pharmacists, patients, admin

class MessagingSystem {
    constructor() {
        this.messages = this.loadMessages();
        this.currentUser = null;
        this.messageListeners = [];
    }

    // Initialize messaging system for current user
    initialize(user) {
        this.currentUser = user;
        this.loadMessages();
        console.log('Messaging system initialized for:', user.name, '(' + user.role + ')');
    }

    // Send a message
    sendMessage(recipientId, recipientRole, subject, content, priority = 'normal') {
        if (!this.currentUser) {
            throw new Error('User not initialized');
        }

        const message = {
            id: this.generateMessageId(),
            senderId: this.currentUser.id,
            senderName: this.currentUser.name,
            senderRole: this.currentUser.role,
            recipientId: recipientId,
            recipientRole: recipientRole,
            subject: subject,
            content: content,
            priority: priority, // 'low', 'normal', 'high', 'urgent'
            timestamp: new Date().toISOString(),
            read: false,
            replied: false,
            threadId: null, // For message threads
            attachments: []
        };

        this.messages.push(message);
        this.saveMessages();
        
        // Trigger message listeners
        this.notifyListeners('messageSent', message);
        
        return message;
    }

    // Reply to a message
    replyToMessage(originalMessageId, content) {
        const originalMessage = this.messages.find(m => m.id === originalMessageId);
        if (!originalMessage) {
            throw new Error('Original message not found');
        }

        const reply = {
            id: this.generateMessageId(),
            senderId: this.currentUser.id,
            senderName: this.currentUser.name,
            senderRole: this.currentUser.role,
            recipientId: originalMessage.senderId,
            recipientRole: originalMessage.senderRole,
            subject: 'Re: ' + originalMessage.subject,
            content: content,
            priority: originalMessage.priority,
            timestamp: new Date().toISOString(),
            read: false,
            replied: false,
            threadId: originalMessage.threadId || originalMessageId,
            attachments: []
        };

        // Mark original message as replied
        originalMessage.replied = true;

        this.messages.push(reply);
        this.saveMessages();
        
        // Trigger message listeners
        this.notifyListeners('messageReplied', reply);
        
        return reply;
    }

    // Get messages for current user
    getMessagesForUser(userId = null, role = null) {
        const targetUserId = userId || this.currentUser.id;
        const targetRole = role || this.currentUser.role;
        
        return this.messages.filter(message => 
            (message.recipientId === targetUserId && message.recipientRole === targetRole) ||
            (message.senderId === targetUserId && message.senderRole === targetRole)
        ).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }

    // Get unread messages count
    getUnreadCount(userId = null, role = null) {
        const targetUserId = userId || this.currentUser.id;
        const targetRole = role || this.currentUser.role;
        
        return this.messages.filter(message => 
            message.recipientId === targetUserId && 
            message.recipientRole === targetRole && 
            !message.read
        ).length;
    }

    // Mark message as read
    markAsRead(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            message.read = true;
            message.readAt = new Date().toISOString();
            this.saveMessages();
            this.notifyListeners('messageRead', message);
        }
    }

    // Get message thread
    getMessageThread(threadId) {
        return this.messages.filter(m => 
            m.id === threadId || m.threadId === threadId
        ).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    }

    // Search messages
    searchMessages(query, userId = null, role = null) {
        const userMessages = this.getMessagesForUser(userId, role);
        const searchTerm = query.toLowerCase();
        
        return userMessages.filter(message => 
            message.subject.toLowerCase().includes(searchTerm) ||
            message.content.toLowerCase().includes(searchTerm) ||
            message.senderName.toLowerCase().includes(searchTerm)
        );
    }

    // Get all users for messaging (simplified - in real app would come from user database)
    getAllUsers() {
        const users = [];
        
        // Get doctors
        const doctors = JSON.parse(localStorage.getItem('doctorCredentials') || '{}');
        Object.values(doctors).forEach(doctor => {
            users.push({
                id: doctor.id,
                name: doctor.name,
                role: 'doctor',
                department: doctor.department || 'General Medicine'
            });
        });

        // Get nurses
        const nurses = JSON.parse(localStorage.getItem('nurseCredentials') || '{}');
        Object.values(nurses).forEach(nurse => {
            users.push({
                id: nurse.id,
                name: nurse.name,
                role: 'nurse',
                department: nurse.department || 'General Nursing'
            });
        });

        // Get pharmacists
        const pharmacists = JSON.parse(localStorage.getItem('pharmacistCredentials') || '{}');
        Object.values(pharmacists).forEach(pharmacist => {
            users.push({
                id: pharmacist.id,
                name: pharmacist.name,
                role: 'pharmacist',
                department: 'Pharmacy'
            });
        });

        // Get patients
        const patients = JSON.parse(localStorage.getItem('patientCredentials') || '{}');
        Object.values(patients).forEach(patient => {
            users.push({
                id: patient.id,
                name: patient.name,
                role: 'patient',
                department: patient.department || 'Student'
            });
        });

        return users;
    }

    // Add message listener
    addListener(callback) {
        this.messageListeners.push(callback);
    }

    // Remove message listener
    removeListener(callback) {
        const index = this.messageListeners.indexOf(callback);
        if (index > -1) {
            this.messageListeners.splice(index, 1);
        }
    }

    // Notify all listeners
    notifyListeners(event, data) {
        this.messageListeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in message listener:', error);
            }
        });
    }

    // Generate unique message ID
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Load messages from localStorage
    loadMessages() {
        this.messages = JSON.parse(localStorage.getItem('hospitalMessages') || '[]');
        return this.messages;
    }

    // Save messages to localStorage
    saveMessages() {
        localStorage.setItem('hospitalMessages', JSON.stringify(this.messages));
    }

    // Create sample messages for testing
    createSampleMessages() {
        const sampleMessages = [
            {
                id: 'msg_sample_1',
                senderId: 'DOC001',
                senderName: 'Dr. Smith',
                senderRole: 'doctor',
                recipientId: '22A/UE/BNS/1001',
                recipientRole: 'patient',
                subject: 'Lab Results Available',
                content: 'Your recent blood test results are now available. Please schedule a follow-up appointment to discuss the findings.',
                priority: 'normal',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                read: false,
                replied: false,
                threadId: null,
                attachments: []
            },
            {
                id: 'msg_sample_2',
                senderId: 'NUR001',
                senderName: 'Nurse Johnson',
                senderRole: 'nurse',
                recipientId: '22A/UE/BNS/1001',
                recipientRole: 'patient',
                subject: 'Medication Reminder',
                content: 'This is a reminder to take your prescribed medication as directed. If you have any questions, please contact us.',
                priority: 'normal',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
                read: false,
                replied: false,
                threadId: null,
                attachments: []
            },
            {
                id: 'msg_sample_3',
                senderId: 'PHAR001',
                senderName: 'Pharmacist Williams',
                senderRole: 'pharmacist',
                recipientId: '22A/UE/BNS/1001',
                recipientRole: 'patient',
                subject: 'Prescription Ready for Pickup',
                content: 'Your prescription is ready for pickup at the pharmacy. Please bring your student ID when collecting.',
                priority: 'normal',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
                read: true,
                replied: false,
                threadId: null,
                attachments: []
            }
        ];

        // Only add sample messages if no messages exist
        if (this.messages.length === 0) {
            this.messages = sampleMessages;
            this.saveMessages();
        }
    }

    // Get message statistics
    getMessageStats(userId = null, role = null) {
        const userMessages = this.getMessagesForUser(userId, role);
        const sent = userMessages.filter(m => m.senderId === (userId || this.currentUser.id));
        const received = userMessages.filter(m => m.recipientId === (userId || this.currentUser.id));
        const unread = received.filter(m => !m.read);
        
        return {
            total: userMessages.length,
            sent: sent.length,
            received: received.length,
            unread: unread.length,
            replied: received.filter(m => m.replied).length
        };
    }
}

// Create global messaging system instance
const messagingSystem = new MessagingSystem();

// Make it globally available
window.messagingSystem = messagingSystem;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MessagingSystem;
}
