<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Appointment - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index-redesigned.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index-redesigned.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="patient-login.html">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="patient-registration.html">Register</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Appointment Booking Section -->
    <section class="py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h1 class="display-5 fw-bold text-dark">Book an Appointment</h1>
                    <p class="lead text-muted">Schedule your visit with our healthcare professionals</p>
                </div>
            </div>

            <div class="row">
                <!-- Appointment Form -->
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-primary text-white py-4">
                            <h4 class="mb-0">
                                <i class="fas fa-calendar-check me-2"></i>
                                Appointment Details
                            </h4>
                        </div>
                        <div class="card-body p-4">
                            <form id="appointmentForm" novalidate>
                                <!-- Patient Information -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-user me-2"></i>Patient Information
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="patientName" class="form-label">Full Name *</label>
                                            <input type="text" class="form-control" id="patientName" name="patientName" required>
                                            <div class="invalid-feedback">Please provide your full name.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="patientEmail" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="patientEmail" name="patientEmail" required>
                                            <div class="invalid-feedback">Please provide a valid email address.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="patientPhone" class="form-label">Phone Number *</label>
                                            <input type="tel" class="form-control" id="patientPhone" name="patientPhone" required>
                                            <div class="invalid-feedback">Please provide a valid phone number.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="patientAge" class="form-label">Age *</label>
                                            <input type="number" class="form-control" id="patientAge" name="patientAge" min="1" max="120" required>
                                            <div class="invalid-feedback">Please provide a valid age.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Department Selection -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-hospital me-2"></i>Department & Doctor
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="department" class="form-label">Department *</label>
                                            <select class="form-select" id="department" name="department" required>
                                                <option value="">Select Department</option>
                                                <option value="cardiology">Cardiology</option>
                                                <option value="dermatology">Dermatology</option>
                                                <option value="emergency">Emergency Medicine</option>
                                                <option value="general">General Medicine</option>
                                                <option value="gynecology">Gynecology</option>
                                                <option value="neurology">Neurology</option>
                                                <option value="orthopedics">Orthopedics</option>
                                                <option value="pediatrics">Pediatrics</option>
                                                <option value="psychiatry">Psychiatry</option>
                                                <option value="surgery">Surgery</option>
                                            </select>
                                            <div class="invalid-feedback">Please select a department.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="doctor" class="form-label">Preferred Doctor</label>
                                            <select class="form-select" id="doctor" name="doctor">
                                                <option value="">Any Available Doctor</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Date and Time Selection -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-clock me-2"></i>Date & Time
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="appointmentDate" class="form-label">Preferred Date *</label>
                                            <input type="date" class="form-control" id="appointmentDate" name="appointmentDate" required>
                                            <div class="invalid-feedback">Please select an appointment date.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="appointmentTime" class="form-label">Preferred Time *</label>
                                            <select class="form-select" id="appointmentTime" name="appointmentTime" required>
                                                <option value="">Select Time</option>
                                                <option value="09:00">09:00 AM</option>
                                                <option value="09:30">09:30 AM</option>
                                                <option value="10:00">10:00 AM</option>
                                                <option value="10:30">10:30 AM</option>
                                                <option value="11:00">11:00 AM</option>
                                                <option value="11:30">11:30 AM</option>
                                                <option value="14:00">02:00 PM</option>
                                                <option value="14:30">02:30 PM</option>
                                                <option value="15:00">03:00 PM</option>
                                                <option value="15:30">03:30 PM</option>
                                                <option value="16:00">04:00 PM</option>
                                                <option value="16:30">04:30 PM</option>
                                            </select>
                                            <div class="invalid-feedback">Please select an appointment time.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Appointment Type -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-stethoscope me-2"></i>Appointment Type
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="appointmentType" class="form-label">Type *</label>
                                            <select class="form-select" id="appointmentType" name="appointmentType" required>
                                                <option value="">Select Type</option>
                                                <option value="consultation">Consultation</option>
                                                <option value="follow-up">Follow-up</option>
                                                <option value="emergency">Emergency</option>
                                                <option value="routine-checkup">Routine Checkup</option>
                                                <option value="vaccination">Vaccination</option>
                                            </select>
                                            <div class="invalid-feedback">Please select appointment type.</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="normal">Normal</option>
                                                <option value="urgent">Urgent</option>
                                                <option value="emergency">Emergency</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Symptoms/Reason -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-notes-medical me-2"></i>Reason for Visit
                                    </h5>
                                    <div class="mb-3">
                                        <label for="symptoms" class="form-label">Symptoms/Reason for Visit *</label>
                                        <textarea class="form-control" id="symptoms" name="symptoms" rows="4" required placeholder="Please describe your symptoms or reason for the appointment..."></textarea>
                                        <div class="invalid-feedback">Please provide the reason for your visit.</div>
                                    </div>
                                </div>

                                <!-- Insurance Information -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-shield-alt me-2"></i>Insurance (Optional)
                                    </h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="insuranceProvider" class="form-label">Insurance Provider</label>
                                            <input type="text" class="form-control" id="insuranceProvider" name="insuranceProvider">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="policyNumber" class="form-label">Policy Number</label>
                                            <input type="text" class="form-control" id="policyNumber" name="policyNumber">
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calendar-check me-2"></i>Book Appointment
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Appointment Information Sidebar -->
                <div class="col-lg-4">
                    <div class="card shadow border-0 rounded-lg mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2 text-primary"></i>
                                Appointment Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-clock me-2"></i>Operating Hours
                                </h6>
                                <p class="text-muted mb-0">Monday - Friday: 8:00 AM - 6:00 PM</p>
                                <p class="text-muted mb-0">Saturday: 9:00 AM - 4:00 PM</p>
                                <p class="text-muted">Sunday: Emergency Only</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-phone me-2"></i>Contact
                                </h6>
                                <p class="text-muted mb-0">Phone: +234 XXX XXX XXXX</p>
                                <p class="text-muted">Email: <EMAIL></p>
                            </div>
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Important Notes
                                </h6>
                                <ul class="text-muted small">
                                    <li>Please arrive 15 minutes before your appointment</li>
                                    <li>Bring a valid ID and insurance card</li>
                                    <li>Cancellations must be made 24 hours in advance</li>
                                    <li>Emergency cases will be prioritized</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="card shadow border-0 rounded-lg bg-danger text-white">
                        <div class="card-body text-center">
                            <h5 class="mb-3">
                                <i class="fas fa-ambulance me-2"></i>Emergency?
                            </h5>
                            <p class="mb-3">For medical emergencies, call immediately or visit our emergency department.</p>
                            <a href="tel:+234XXXXXXXXX" class="btn btn-light btn-lg">
                                <i class="fas fa-phone me-2"></i>Call Emergency
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Final Year Project - Newgate University Minna</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/appointment.js"></script>
</body>
</html>
