<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Personalized Dashboard - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h3 class="mb-0"><i class="fas fa-user-check me-2"></i>Test Personalized Patient Dashboard</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Personalization Test</h5>
                            <p class="mb-2">This test verifies that patients see their own data in the dashboard:</p>
                            <ol class="mb-0">
                                <li><strong>Register a new patient</strong> with unique details</li>
                                <li><strong>Login with new credentials</strong></li>
                                <li><strong>Verify dashboard shows correct personal information</strong></li>
                                <li><strong>Check medical records section</strong> for accurate data</li>
                            </ol>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-user-plus me-2 text-success"></i>Step 1: Register Test Patient</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>Use These Test Details:</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <small>
                                                    <strong>First Name:</strong> TestUser<br>
                                                    <strong>Last Name:</strong> PersonalData<br>
                                                    <strong>Email:</strong> <EMAIL><br>
                                                    <strong>Phone:</strong> +234-999-TEST-001<br>
                                                </small>
                                            </div>
                                            <div class="col-6">
                                                <small>
                                                    <strong>DOB:</strong> 1995-06-15<br>
                                                    <strong>Gender:</strong> Male<br>
                                                    <strong>City:</strong> TestCity<br>
                                                    <strong>Password:</strong> testuser123<br>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a href="patient-registration.html" class="btn btn-success w-100 mb-3">
                                    <i class="fas fa-user-plus me-2"></i>Register Test Patient
                                </a>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-sign-in-alt me-2 text-primary"></i>Step 2: Login & Test</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>After Registration:</h6>
                                        <small>
                                            1. Note your <strong>Patient ID</strong> (e.g., PT009)<br>
                                            2. Login with Patient ID + password<br>
                                            3. Check dashboard for your details<br>
                                            4. Verify medical records section<br>
                                        </small>
                                    </div>
                                </div>
                                <a href="patient-login.html" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login to Test
                                </a>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-check-circle me-2 text-success"></i>What Should Show Your Data:</h5>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        <strong>Welcome Message:</strong> Your actual name
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-id-card me-2 text-info"></i>
                                        <strong>Patient ID:</strong> Your generated ID
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-envelope me-2 text-warning"></i>
                                        <strong>Email:</strong> Your registration email
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-phone me-2 text-success"></i>
                                        <strong>Phone:</strong> Your registration phone
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-birthday-cake me-2 text-danger"></i>
                                        <strong>Date of Birth:</strong> Your registration DOB
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-map-marker-alt me-2 text-info"></i>
                                        <strong>Address:</strong> Your registration address
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-tools me-2 text-warning"></i>Debug Tools</h5>
                                
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="showCurrentUser()">
                                    <i class="fas fa-user me-2"></i>Show Current Logged User
                                </button>
                                
                                <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="showAllRegistered()">
                                    <i class="fas fa-users me-2"></i>Show All Registered Patients
                                </button>
                                
                                <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="showMedicalData()">
                                    <i class="fas fa-file-medical me-2"></i>Show Medical Data
                                </button>
                                
                                <button class="btn btn-outline-danger btn-sm w-100" onclick="clearAllData()">
                                    <i class="fas fa-trash me-2"></i>Clear All Test Data
                                </button>
                                
                                <div id="debugOutput" class="mt-3"></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>Expected Results:</h6>
                            <ul class="mb-0">
                                <li><strong>Dashboard:</strong> Shows "Welcome, TestUser PersonalData"</li>
                                <li><strong>Medical Records:</strong> Shows your registration details</li>
                                <li><strong>Personal Info:</strong> Email: <EMAIL></li>
                                <li><strong>Contact Info:</strong> Phone: +234-999-TEST-001</li>
                                <li><strong>Demographics:</strong> DOB: June 15, 1995, Gender: Male</li>
                                <li><strong>Location:</strong> TestCity address information</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>If You See Wrong Data:</h6>
                            <ul class="mb-0">
                                <li>Check if you're logged in with the correct Patient ID</li>
                                <li>Use debug tools to verify current user data</li>
                                <li>Clear browser cache and try again</li>
                                <li>Check console (F12) for any JavaScript errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showCurrentUser() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            const output = document.getElementById('debugOutput');
            
            if (!userData) {
                output.innerHTML = '<div class="alert alert-warning">No user currently logged in.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            output.innerHTML = `
                <div class="alert alert-info">
                    <h6>Current Logged User:</h6>
                    <pre>${JSON.stringify(user, null, 2)}</pre>
                </div>
            `;
        }
        
        function showAllRegistered() {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const output = document.getElementById('debugOutput');
            
            if (Object.keys(registeredPatients).length === 0) {
                output.innerHTML = '<div class="alert alert-warning">No registered patients found.</div>';
                return;
            }
            
            let html = '<div class="alert alert-info"><h6>All Registered Patients:</h6>';
            Object.keys(registeredPatients).forEach(id => {
                const patient = registeredPatients[id];
                html += `<p><strong>${id}:</strong> ${patient.name} (${patient.email})</p>`;
            });
            html += '</div>';
            
            output.innerHTML = html;
        }
        
        function showMedicalData() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            const output = document.getElementById('debugOutput');
            
            if (!userData) {
                output.innerHTML = '<div class="alert alert-warning">No user logged in to show medical data.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            const medicalData = localStorage.getItem(`medicalData_${user.id}`);
            
            if (!medicalData) {
                output.innerHTML = '<div class="alert alert-warning">No medical data found for current user.</div>';
                return;
            }
            
            output.innerHTML = `
                <div class="alert alert-info">
                    <h6>Medical Data for ${user.name} (${user.id}):</h6>
                    <pre>${JSON.stringify(JSON.parse(medicalData), null, 2)}</pre>
                </div>
            `;
        }
        
        function clearAllData() {
            if (confirm('Are you sure you want to clear all test data? This will remove all registered patients and medical data.')) {
                localStorage.removeItem('registeredPatients');
                localStorage.removeItem('hospitalUser');
                sessionStorage.clear();
                
                // Clear all medical data
                Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('medicalData_')) {
                        localStorage.removeItem(key);
                    }
                });
                
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-success">All test data cleared successfully!</div>';
            }
        }
    </script>
</body>
</html>
