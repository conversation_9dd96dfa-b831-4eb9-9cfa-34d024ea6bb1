<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Matric Number System - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Matric Number System Test
                        </h2>
                        <p class="mb-0 mt-2">Test the complete student registration and login system</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>System Changes Implemented</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>Matric Number Format:</strong> 22B/UE/BSE/1005</li>
                                        <li>✅ <strong>Simplified Registration:</strong> Removed optional fields</li>
                                        <li>✅ <strong>Student-Focused:</strong> Changed from "Patient" to "Student"</li>
                                        <li>✅ <strong>Single Login Portal:</strong> One unified login system</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>Centered UI:</strong> Login/Register buttons centered</li>
                                        <li>✅ <strong>Removed Testimonials:</strong> Cleaner home page</li>
                                        <li>✅ <strong>Professional Footer:</strong> Updated footer text</li>
                                        <li>✅ <strong>Color Standards:</strong> Applied throughout system</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-user-plus me-2 text-success"></i>Test Registration</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>Sample Matric Numbers to Test:</h6>
                                        <div class="row">
                                            <div class="col-12">
                                                <small class="font-monospace">
                                                    <strong>BSE Department:</strong><br>
                                                    22A/UE/BSE/1001, 22B/UE/BSE/1005<br>
                                                    <strong>BNS Department:</strong><br>
                                                    22A/UE/BNS/1001, 22B/UE/BNS/1001<br>
                                                    <strong>Law Department:</strong><br>
                                                    22A/UE/LLB/1001, 23A/UE/LLB/2001<br>
                                                    <strong>Short Format:</strong><br>
                                                    23A, 23B, 24A, 24B
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a href="patient-registration.html" class="btn btn-success w-100 mb-3 interactive-hover">
                                    <i class="fas fa-user-plus me-2"></i>Test Student Registration
                                </a>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-sign-in-alt me-2 text-primary"></i>Test Login</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>After Registration:</h6>
                                        <small>
                                            1. Use your <strong>Matric Number</strong> as login ID<br>
                                            2. Enter the <strong>password</strong> you set<br>
                                            3. Access your <strong>student dashboard</strong><br>
                                            4. Verify your <strong>personal information</strong>
                                        </small>
                                    </div>
                                </div>
                                <a href="patient-login.html" class="btn btn-primary w-100 mb-3 interactive-hover">
                                    <i class="fas fa-sign-in-alt me-2"></i>Test Student Login
                                </a>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-list-check me-2 text-info"></i>Registration Fields</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Field</th>
                                                <th>Required</th>
                                                <th>Notes</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>Matric Number</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Multiple formats supported</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Full Name</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Complete name</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Date of Birth</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>YYYY-MM-DD format</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Gender</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Male/Female</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Department</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>BSE, BNS, LLB, etc.</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Blood Group</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>A+, B+, O+, etc.</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Email</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Must be unique</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Contact number</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Password</strong></td>
                                                <td><span class="badge bg-danger">Yes</span></td>
                                                <td>Minimum 6 characters</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-tools me-2 text-warning"></i>Debug Tools</h5>
                                
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="showRegisteredStudents()">
                                    <i class="fas fa-users me-2"></i>Show Registered Students
                                </button>
                                
                                <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="validateMatricFormat()">
                                    <i class="fas fa-check me-2"></i>Test Matric Format Validation
                                </button>
                                
                                <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="testLoginSystem()">
                                    <i class="fas fa-sign-in-alt me-2"></i>Test Login System
                                </button>
                                
                                <button class="btn btn-outline-danger btn-sm w-100" onclick="clearAllData()">
                                    <i class="fas fa-trash me-2"></i>Clear All Test Data
                                </button>
                                
                                <div id="debugOutput" class="mt-3" style="max-height: 300px; overflow-y: auto;"></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-4">
                            <h6><i class="fas fa-check-circle me-2"></i>Expected Results:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Registration:</strong> Uses matric number as unique ID</li>
                                        <li><strong>Validation:</strong> Enforces correct matric format</li>
                                        <li><strong>Login:</strong> Uses matric number instead of patient ID</li>
                                        <li><strong>Dashboard:</strong> Shows "Student" instead of "Patient"</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Simplified Form:</strong> Only essential fields</li>
                                        <li><strong>Centered UI:</strong> Better visual hierarchy</li>
                                        <li><strong>Single Portal:</strong> One login system</li>
                                        <li><strong>Professional Design:</strong> Consistent colors</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-outline-primary me-3">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                            <a href="color-theme-demo.html" class="btn btn-outline-info">
                                <i class="fas fa-palette me-2"></i>View Color Theme
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showRegisteredStudents() {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const output = document.getElementById('debugOutput');
            
            if (Object.keys(registeredPatients).length === 0) {
                output.innerHTML = '<div class="alert alert-warning">No registered students found.</div>';
                return;
            }
            
            let html = '<div class="alert alert-info"><h6>Registered Students:</h6>';
            Object.keys(registeredPatients).forEach(matricNumber => {
                const student = registeredPatients[matricNumber];
                html += `<p><strong>${matricNumber}:</strong> ${student.name} (${student.email})</p>`;
            });
            html += '</div>';
            
            output.innerHTML = html;
        }
        
        function validateMatricFormat() {
            const testMatrics = [
                '22A/UE/BSE/1001', // Valid - BSE A
                '22B/UE/BSE/1005', // Valid - BSE B
                '22A/UE/BNS/1001', // Valid - BNS A
                '22B/UE/BNS/1001', // Valid - BNS B
                '22A/UE/LLB/1001', // Valid - Law A
                '23A',             // Valid - Short format
                '23B',             // Valid - Short format
                '2B/UE/BSE/1005',  // Invalid (year too short)
                '22B/UE/BSE/100',  // Invalid (number too short)
                '22B/UE/CSE/1005', // Invalid (wrong department)
                'PT001'            // Invalid (old format)
            ];

            const matricPatterns = [
                /^[0-9]{2}[AB]\/UE\/BSE\/[0-9]{4}$/,    // BSE
                /^[0-9]{2}[AB]\/UE\/BNS\/[0-9]{4}$/,    // BNS
                /^[0-9]{2}[AB]\/UE\/LLB\/[0-9]{4}$/,    // Law
                /^[0-9]{2}[AB]\/UE\/ENG\/[0-9]{4}$/,    // Engineering
                /^[0-9]{2}[AB]\/UE\/EDU\/[0-9]{4}$/,    // Education
                /^[0-9]{2}[AB]\/UE\/AGR\/[0-9]{4}$/,    // Agriculture
                /^[0-9]{2}[AB]\/UE\/ENV\/[0-9]{4}$/,    // Environmental
                /^[0-9]{2}[AB]\/UE\/ICT\/[0-9]{4}$/,    // ICT
                /^[0-9]{2}[AB]$/,                       // Short format
            ];
            
            let html = '<div class="alert alert-info"><h6>Matric Format Validation Test:</h6>';
            testMatrics.forEach(matric => {
                const isValid = matricPatterns.some(pattern => pattern.test(matric));
                const badge = isValid ? 'bg-success' : 'bg-danger';
                const icon = isValid ? 'fa-check' : 'fa-times';
                html += `<p><span class="badge ${badge}"><i class="fas ${icon} me-1"></i></span> ${matric}</p>`;
            });
            html += '</div>';
            
            document.getElementById('debugOutput').innerHTML = html;
        }
        
        function testLoginSystem() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            const output = document.getElementById('debugOutput');
            
            if (!userData) {
                output.innerHTML = '<div class="alert alert-warning">No user currently logged in. Please login to test.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            const isMatricFormat = /^[0-9]{2}B\/UE\/BSE\/[0-9]{4}$/.test(user.id);
            
            let html = '<div class="alert alert-success"><h6>Login System Test Results:</h6>';
            html += `<p><strong>User ID:</strong> ${user.id}</p>`;
            html += `<p><strong>Format Check:</strong> ${isMatricFormat ? '✅ Valid Matric Number' : '❌ Not Matric Format'}</p>`;
            html += `<p><strong>Name:</strong> ${user.name}</p>`;
            html += `<p><strong>Email:</strong> ${user.email}</p>`;
            html += `<p><strong>User Type:</strong> ${user.userType || 'Not specified'}</p>`;
            html += '</div>';
            
            output.innerHTML = html;
        }
        
        function clearAllData() {
            if (confirm('Are you sure you want to clear all test data? This will remove all registered students.')) {
                localStorage.removeItem('registeredPatients');
                localStorage.removeItem('hospitalUser');
                sessionStorage.clear();
                
                // Clear all medical data
                Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('medicalData_') || key.startsWith('billingData_')) {
                        localStorage.removeItem(key);
                    }
                });
                
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-success">✅ All test data cleared successfully!</div>';
            }
        }
    </script>
</body>
</html>
