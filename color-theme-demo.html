<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newgate Hospital - Color Theme Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index-redesigned.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="patient-login.html">Patient Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="patient-registration.html">Register</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow-lg fade-in-up">
                    <div class="card-header text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-palette me-2"></i>
                            Newgate Hospital - Interactive Color Theme
                        </h2>
                        <p class="mb-0 mt-2">Professional Healthcare Management System</p>
                    </div>
                    <div class="card-body">
                        <!-- Color Palette Display -->
                        <div class="row mb-5">
                            <div class="col-12">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-swatchbook me-2"></i>Color Standards
                                </h4>
                                <div class="row g-3">
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #007BFF; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Primary</strong><br>#007BFF</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #6C757D; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Secondary</strong><br>#6C757D</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #28A745; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Success</strong><br>#28A745</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #FFC107; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Warning</strong><br>#FFC107</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #DC3545; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Danger</strong><br>#DC3545</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <div class="card-body p-2">
                                                <div style="background: #17A2B8; height: 60px; border-radius: 8px;" class="mb-2"></div>
                                                <small><strong>Info</strong><br>#17A2B8</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Interactive Buttons -->
                        <div class="row mb-5">
                            <div class="col-12">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-mouse-pointer me-2"></i>Interactive Buttons
                                </h4>
                                <div class="row g-3">
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-primary w-100 interactive-hover">
                                            <i class="fas fa-user me-2"></i>Primary
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-success w-100 interactive-hover">
                                            <i class="fas fa-check me-2"></i>Success
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-warning w-100 interactive-hover">
                                            <i class="fas fa-exclamation me-2"></i>Warning
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-danger w-100 interactive-hover">
                                            <i class="fas fa-times me-2"></i>Danger
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-info w-100 interactive-hover">
                                            <i class="fas fa-info me-2"></i>Info
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                        <button class="btn btn-secondary w-100 interactive-hover">
                                            <i class="fas fa-cog me-2"></i>Secondary
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dashboard Cards Demo -->
                        <div class="row mb-5">
                            <div class="col-12">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-th-large me-2"></i>Dashboard Cards
                                </h4>
                                <div class="row g-3">
                                    <div class="col-lg-3 col-md-6">
                                        <div class="card dashboard-card text-center interactive-hover fade-in-up">
                                            <div class="card-body">
                                                <div class="text-primary mb-2">
                                                    <i class="fas fa-users fa-2x"></i>
                                                </div>
                                                <h4 class="fw-bold text-primary">1,234</h4>
                                                <p class="text-muted mb-0">Total Patients</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <div class="card dashboard-card text-center interactive-hover fade-in-up" style="animation-delay: 0.1s;">
                                            <div class="card-body">
                                                <div class="text-success mb-2">
                                                    <i class="fas fa-calendar-check fa-2x"></i>
                                                </div>
                                                <h4 class="fw-bold text-success">567</h4>
                                                <p class="text-muted mb-0">Appointments</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <div class="card dashboard-card text-center interactive-hover fade-in-up" style="animation-delay: 0.2s;">
                                            <div class="card-body">
                                                <div class="text-info mb-2">
                                                    <i class="fas fa-user-md fa-2x"></i>
                                                </div>
                                                <h4 class="fw-bold text-info">89</h4>
                                                <p class="text-muted mb-0">Doctors</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <div class="card dashboard-card text-center interactive-hover fade-in-up" style="animation-delay: 0.3s;">
                                            <div class="card-body">
                                                <div class="text-warning mb-2">
                                                    <i class="fas fa-hospital fa-2x"></i>
                                                </div>
                                                <h4 class="fw-bold text-warning">12</h4>
                                                <p class="text-muted mb-0">Departments</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Alerts Demo -->
                        <div class="row mb-5">
                            <div class="col-12">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-bell me-2"></i>Alert Messages
                                </h4>
                                <div class="alert alert-primary" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Primary Alert!</strong> This is a primary alert with the new color scheme.
                                </div>
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Success!</strong> Your action was completed successfully.
                                </div>
                                <div class="alert alert-warning" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning!</strong> Please review this information carefully.
                                </div>
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <strong>Error!</strong> Something went wrong. Please try again.
                                </div>
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>Info!</strong> Here's some helpful information for you.
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <div class="row">
                            <div class="col-12">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-link me-2"></i>Quick Navigation
                                </h4>
                                <div class="row g-3">
                                    <div class="col-lg-3 col-md-6">
                                        <a href="index-redesigned.html" class="btn btn-outline-primary w-100 interactive-hover">
                                            <i class="fas fa-home me-2"></i>Home Page
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <a href="patient-login.html" class="btn btn-outline-success w-100 interactive-hover">
                                            <i class="fas fa-sign-in-alt me-2"></i>Patient Login
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <a href="patient-registration.html" class="btn btn-outline-info w-100 interactive-hover">
                                            <i class="fas fa-user-plus me-2"></i>Register
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        <a href="patient-dashboard.html" class="btn btn-outline-warning w-100 interactive-hover">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Enhanced with Professional Color Standards</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effects to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
    </script>
    
    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .btn {
            position: relative;
            overflow: hidden;
        }
    </style>
</body>
</html>
