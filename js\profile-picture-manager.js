// Universal Profile Picture Management System - Newgate Hospital Management System

class ProfilePictureManager {
    constructor() {
        this.defaultAvatars = {
            student: 'https://cdn-icons-png.flaticon.com/128/3135/3135715.png',
            nurse: 'https://cdn-icons-png.flaticon.com/128/2785/2785482.png',
            doctor: 'https://cdn-icons-png.flaticon.com/128/2785/2785491.png',
            admin: 'https://cdn-icons-png.flaticon.com/128/1077/1077114.png',
            pharmacist: 'https://cdn-icons-png.flaticon.com/128/2785/2785491.png'
        };
        
        this.predefinedAvatars = [
            'https://cdn-icons-png.flaticon.com/128/3135/3135715.png',
            'https://cdn-icons-png.flaticon.com/128/3135/3135768.png',
            'https://cdn-icons-png.flaticon.com/128/3135/3135789.png',
            'https://cdn-icons-png.flaticon.com/128/3135/3135823.png',
            'https://cdn-icons-png.flaticon.com/128/2785/2785482.png',
            'https://cdn-icons-png.flaticon.com/128/2785/2785491.png',
            'https://cdn-icons-png.flaticon.com/128/1077/1077114.png',
            'https://cdn-icons-png.flaticon.com/128/1077/1077063.png',
            'https://cdn-icons-png.flaticon.com/128/4140/4140037.png',
            'https://cdn-icons-png.flaticon.com/128/4140/4140048.png',
            'https://cdn-icons-png.flaticon.com/128/4140/4140061.png',
            'https://cdn-icons-png.flaticon.com/128/4140/4140047.png'
        ];
    }

    // Initialize profile picture functionality
    initializeProfilePicture(userId, userRole) {
        this.currentUserId = userId;
        this.currentUserRole = userRole;
        this.loadCurrentProfilePicture();
    }

    // Load current profile picture
    loadCurrentProfilePicture() {
        const savedPicture = localStorage.getItem(`profilePicture_${this.currentUserId}`);
        if (savedPicture) {
            this.updateProfilePictureDisplay(savedPicture);
        } else {
            // Use default avatar based on role
            const defaultAvatar = this.defaultAvatars[this.currentUserRole] || this.defaultAvatars.student;
            this.updateProfilePictureDisplay(defaultAvatar);
        }
    }

    // Show profile picture change modal
    showProfilePictureModal() {
        this.createProfilePictureModal();
        const modal = new bootstrap.Modal(document.getElementById('profilePictureModal'));
        modal.show();
    }

    // Create profile picture modal
    createProfilePictureModal() {
        // Remove existing modal if present
        const existingModal = document.getElementById('profilePictureModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modalHTML = `
            <div class="modal fade" id="profilePictureModal" tabindex="-1" aria-labelledby="profilePictureModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="profilePictureModalLabel">
                                <i class="fas fa-camera me-2"></i>Change Profile Picture
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h6>Current Picture</h6>
                                        <img id="currentProfilePreview" src="" alt="Current Profile" class="rounded-circle border border-primary mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                                        <div>
                                            <button class="btn btn-outline-danger btn-sm" onclick="profilePictureManager.removeProfilePicture()">
                                                <i class="fas fa-trash me-2"></i>Remove Picture
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="mb-4">
                                        <h6><i class="fas fa-upload me-2"></i>Upload from Device</h6>
                                        <div class="upload-area border border-dashed border-primary rounded p-4 text-center" style="cursor: pointer;" onclick="document.getElementById('profilePictureInput').click()">
                                            <i class="fas fa-cloud-upload-alt fa-2x text-primary mb-2"></i>
                                            <p class="mb-0">Click to select image from your device</p>
                                            <small class="text-muted">Supports: JPG, PNG, GIF (Max: 5MB)</small>
                                        </div>
                                        <input type="file" id="profilePictureInput" accept="image/*" style="display: none;" onchange="profilePictureManager.handleFileUpload(event)">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h6><i class="fas fa-images me-2"></i>Choose from Gallery</h6>
                                        <div class="row g-2" id="avatarGallery">
                                            <!-- Avatar gallery will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3" id="imagePreviewSection" style="display: none;">
                                <div class="col-12">
                                    <h6><i class="fas fa-eye me-2"></i>Preview</h6>
                                    <div class="text-center">
                                        <img id="newImagePreview" src="" alt="New Profile Preview" class="rounded-circle border border-success mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                        <div>
                                            <button class="btn btn-success me-2" onclick="profilePictureManager.saveProfilePicture()">
                                                <i class="fas fa-save me-2"></i>Save Picture
                                            </button>
                                            <button class="btn btn-secondary" onclick="profilePictureManager.cancelPreview()">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.populateAvatarGallery();
        this.updateCurrentPreview();
    }

    // Populate avatar gallery
    populateAvatarGallery() {
        const gallery = document.getElementById('avatarGallery');
        gallery.innerHTML = '';

        this.predefinedAvatars.forEach((avatar, index) => {
            const avatarElement = `
                <div class="col-3">
                    <img src="${avatar}" alt="Avatar ${index + 1}" 
                         class="img-fluid rounded-circle border avatar-option" 
                         style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                         onclick="profilePictureManager.selectPredefinedAvatar('${avatar}')">
                </div>
            `;
            gallery.insertAdjacentHTML('beforeend', avatarElement);
        });
    }

    // Update current preview
    updateCurrentPreview() {
        const currentPicture = localStorage.getItem(`profilePicture_${this.currentUserId}`) || 
                              this.defaultAvatars[this.currentUserRole];
        document.getElementById('currentProfilePreview').src = currentPicture;
    }

    // Handle file upload
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showAlert('Please select a valid image file.', 'error');
            return;
        }

        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
            this.showAlert('File size must be less than 5MB.', 'error');
            return;
        }

        // Show loading state
        this.showLoadingState();

        // Read file and show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            this.hideLoadingState();
            this.showImagePreview(e.target.result);
        };
        reader.onerror = () => {
            this.hideLoadingState();
            this.showAlert('Error reading file. Please try again.', 'error');
        };
        reader.readAsDataURL(file);
    }

    // Show loading state during file upload
    showLoadingState() {
        const uploadArea = document.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Processing image...</p>
                </div>
            `;
        }
    }

    // Hide loading state
    hideLoadingState() {
        const uploadArea = document.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.innerHTML = `
                <i class="fas fa-cloud-upload-alt fa-2x text-primary mb-2"></i>
                <p class="mb-0">Click to select image from your device</p>
                <small class="text-muted">Supports: JPG, PNG, GIF (Max: 5MB)</small>
            `;
        }
    }

    // Select predefined avatar
    selectPredefinedAvatar(avatarUrl) {
        this.showImagePreview(avatarUrl);
    }

    // Show image preview
    showImagePreview(imageUrl) {
        document.getElementById('newImagePreview').src = imageUrl;
        document.getElementById('imagePreviewSection').style.display = 'block';
        this.pendingProfilePicture = imageUrl;
    }

    // Cancel preview
    cancelPreview() {
        document.getElementById('imagePreviewSection').style.display = 'none';
        this.pendingProfilePicture = null;
        document.getElementById('profilePictureInput').value = '';
    }

    // Save profile picture
    saveProfilePicture() {
        if (!this.pendingProfilePicture) {
            this.showAlert('No image selected.', 'error');
            return;
        }

        // Save to localStorage
        localStorage.setItem(`profilePicture_${this.currentUserId}`, this.pendingProfilePicture);

        // Update all profile picture displays
        this.updateProfilePictureDisplay(this.pendingProfilePicture);

        // Update user data
        this.updateUserProfilePicture(this.pendingProfilePicture);

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('profilePictureModal'));
        modal.hide();

        this.showAlert('Profile picture updated successfully!', 'success');
    }

    // Remove profile picture
    removeProfilePicture() {
        if (confirm('Are you sure you want to remove your profile picture?')) {
            localStorage.removeItem(`profilePicture_${this.currentUserId}`);
            const defaultAvatar = this.defaultAvatars[this.currentUserRole];
            this.updateProfilePictureDisplay(defaultAvatar);
            this.updateUserProfilePicture(null);
            this.updateCurrentPreview();
            this.showAlert('Profile picture removed successfully!', 'success');
        }
    }

    // Update profile picture display across the interface
    updateProfilePictureDisplay(imageUrl) {
        // Update all profile picture elements
        const profilePictures = document.querySelectorAll('.profile-picture, .user-avatar, .profile-avatar img, .nurse-avatar img, .pharmacist-avatar img, .admin-avatar img');
        profilePictures.forEach(img => {
            img.src = imageUrl;
        });

        // Update specific elements based on context
        const specificSelectors = [
            '#userProfilePicture',
            '#profileAvatar',
            '#dashboardAvatar',
            '#headerAvatar',
            '.profile-image'
        ];

        specificSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                element.src = imageUrl;
            }
        });
    }

    // Update user profile picture in user data
    updateUserProfilePicture(imageUrl) {
        // Update session storage
        const userData = JSON.parse(sessionStorage.getItem('hospitalUser') || '{}');
        if (userData.id) {
            userData.profilePicture = imageUrl;
            sessionStorage.setItem('hospitalUser', JSON.stringify(userData));
            localStorage.setItem('hospitalUser', JSON.stringify(userData));
        }

        // Update specific user databases based on role
        this.updateRoleSpecificDatabase(imageUrl);
    }

    // Update role-specific databases
    updateRoleSpecificDatabase(imageUrl) {
        switch (this.currentUserRole) {
            case 'student':
                this.updateStudentDatabase(imageUrl);
                break;
            case 'nurse':
                this.updateNurseDatabase(imageUrl);
                break;
            case 'pharmacist':
                this.updatePharmacistDatabase(imageUrl);
                break;
            case 'admin':
                this.updateAdminDatabase(imageUrl);
                break;
        }
    }

    // Update student database
    updateStudentDatabase(imageUrl) {
        const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients') || '{}');
        if (registeredPatients[this.currentUserId]) {
            registeredPatients[this.currentUserId].profilePicture = imageUrl;
            localStorage.setItem('registeredPatients', JSON.stringify(registeredPatients));
        }
    }

    // Update nurse database
    updateNurseDatabase(imageUrl) {
        if (window.nurseAccounts && window.nurseAccounts[this.currentUserId]) {
            window.nurseAccounts[this.currentUserId].profilePicture = imageUrl;
        }
    }

    // Update pharmacist database
    updatePharmacistDatabase(imageUrl) {
        if (window.pharmacistAccounts && window.pharmacistAccounts[this.currentUserId]) {
            window.pharmacistAccounts[this.currentUserId].profilePicture = imageUrl;
        }
    }

    // Update admin database
    updateAdminDatabase(imageUrl) {
        // Update admin database when implemented
        console.log('Admin database update:', imageUrl);
    }

    // Show alert message
    showAlert(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Get current profile picture
    getCurrentProfilePicture() {
        return localStorage.getItem(`profilePicture_${this.currentUserId}`) || 
               this.defaultAvatars[this.currentUserRole];
    }
}

// Create global instance
const profilePictureManager = new ProfilePictureManager();

// Make globally available
window.profilePictureManager = profilePictureManager;
