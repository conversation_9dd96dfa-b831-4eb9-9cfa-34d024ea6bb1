// Doctor Dashboard JavaScript - Newgate Hospital Management System

let currentDoctor = null;

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDoctorDashboard();
});

function initializeDoctorDashboard() {
    // Check if user is logged in as doctor
    const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
    
    if (!user.role || user.role !== 'doctor') {
        // Redirect to login if not logged in as doctor
        window.location.href = 'doctor-login.html';
        return;
    }
    
    currentDoctor = user;
    
    // Update UI with doctor information
    updateDoctorInfo();
    
    // Set current date
    updateCurrentDate();
    
    // Load dashboard data
    loadDashboardData();

    // Load doctor settings
    loadDoctorSettings();

    // Show dashboard by default
    showDashboard();
}

function updateDoctorInfo() {
    if (currentDoctor) {
        document.getElementById('doctorName').textContent = currentDoctor.name;
        document.getElementById('welcomeName').textContent = currentDoctor.name;
        document.getElementById('doctorId').textContent = currentDoctor.id;
        document.getElementById('doctorDepartment').textContent = currentDoctor.department || currentDoctor.specialty;
        document.getElementById('doctorSpecialization').textContent = currentDoctor.specialty;
        document.getElementById('doctorExperience').textContent = currentDoctor.experience || '0 years';
    }
}

function updateCurrentDate() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', options);
}

function loadDashboardData() {
    // Load statistics and update dashboard cards
    const stats = getDoctorStats();
    
    // Update stat cards if they exist
    const todayAppointmentsEl = document.getElementById('todayAppointments');
    const totalPatientsEl = document.getElementById('totalPatients');
    const pendingLabsEl = document.getElementById('pendingLabs');
    const unreadMessagesEl = document.getElementById('unreadMessages');
    
    if (todayAppointmentsEl) todayAppointmentsEl.textContent = stats.todayAppointments;
    if (totalPatientsEl) totalPatientsEl.textContent = stats.totalPatients;
    if (pendingLabsEl) pendingLabsEl.textContent = stats.pendingLabs;
    if (unreadMessagesEl) unreadMessagesEl.textContent = stats.unreadMessages;
}

function getDoctorStats() {
    // Get real data from localStorage
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const totalPatients = Object.keys(registeredPatients).length;
    
    return {
        todayAppointments: Math.floor(Math.random() * 8) + 2, // 2-10 appointments
        totalPatients: totalPatients || 25,
        pendingLabs: Math.floor(Math.random() * 5) + 1, // 1-6 pending labs
        unreadMessages: Math.floor(Math.random() * 3) + 1 // 1-4 messages
    };
}

// Section management functions
function showSection(sectionName) {
    // Hide all content sections
    const sections = ['dashboardContent', 'appointmentsContent', 'patientsContent', 'scheduleContent', 
                     'prescriptionsContent', 'labOrdersContent', 'reportsContent', 'messagesContent'];
    
    sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
            element.style.display = 'none';
        }
    });
    
    // Show the requested section
    const targetSection = document.getElementById(sectionName + 'Content');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // Update sidebar active state
    updateSidebarActive(sectionName);
}

function updateSidebarActive(activeSection) {
    // Remove active class from all sidebar links
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => link.classList.remove('active'));
    
    // Add active class to current section
    const activeLinkMap = {
        'dashboard': 0,
        'appointments': 1,
        'patients': 2,
        'schedule': 3,
        'prescriptions': 4,
        'labOrders': 5,
        'reports': 6,
        'messages': 7
    };
    
    const linkIndex = activeLinkMap[activeSection];
    if (linkIndex !== undefined && sidebarLinks[linkIndex]) {
        sidebarLinks[linkIndex].classList.add('active');
    }
}

// Navigation functions
function showDashboard() {
    console.log('Loading Dashboard...');
    showSection('dashboard');
    loadDashboardData();
}

function showAppointments() {
    console.log('Loading Appointments...');
    showSection('appointments');
    loadAppointmentsData();
}

function showPatients() {
    console.log('Loading My Patients...');
    showSection('patients');
    loadPatientsData();
}

function showSchedule() {
    console.log('Loading Schedule...');
    showSection('schedule');
    loadScheduleData();
}

function showPrescriptions() {
    console.log('Loading Prescriptions...');
    showSection('prescriptions');
    loadPrescriptionsData();
}

function showLabOrders() {
    console.log('Loading Lab Orders...');
    showSection('labOrders');
    loadLabOrdersData();
}

function showReports() {
    console.log('Loading Reports...');
    showSection('reports');
    loadReportsData();
}

function showMessages() {
    console.log('Loading Messages...');
    showSection('messages');
    loadMessagesData();
}

// Quick access functions
function showTodaysSchedule() {
    showSchedule();
}

function addPatientRecord() {
    showAddPatientModal();
}

function writePrescription() {
    showPrescriptionModal();
}

function orderLabTest() {
    showLabOrderModal();
}

// Profile and settings functions
function showProfile() {
    showDoctorProfileModal();
}

function showSettings() {
    showDoctorSettingsModal();
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        sessionStorage.removeItem('hospitalUser');
        localStorage.removeItem('hospitalUser');
        window.location.href = 'doctor-login.html';
    }
}

// Utility functions
function showSuccessAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function showErrorAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showAppointments = showAppointments;
window.showPatients = showPatients;
window.showSchedule = showSchedule;
window.showPrescriptions = showPrescriptions;
window.showLabOrders = showLabOrders;
window.showReports = showReports;
window.showMessages = showMessages;
window.showTodaysSchedule = showTodaysSchedule;
window.addPatientRecord = addPatientRecord;
window.writePrescription = writePrescription;
window.orderLabTest = orderLabTest;
window.showProfile = showProfile;
window.showSettings = showSettings;
window.logout = logout;

// Data loading functions
function loadAppointmentsData() {
    const appointmentsContainer = document.getElementById('appointmentsContent');
    if (!appointmentsContainer) return;

    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const studentMatrics = Object.keys(registeredPatients);

    // Generate sample appointments for this doctor
    const appointments = generateDoctorAppointments(studentMatrics);

    let html = `
        <h3 class="mb-4">My Appointments</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="appointmentFilter" onchange="filterAppointments()">
                    <option value="">All Appointments</option>
                    <option value="today">Today</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="completed">Completed</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="addNewAppointment()">
                    <i class="fas fa-plus me-2"></i>New Appointment
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Student</th>
                        <th>Matric Number</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    if (appointments.length === 0) {
        html += '<tr><td colspan="6" class="text-center">No appointments found</td></tr>';
    } else {
        appointments.forEach(appointment => {
            const statusClass = {
                'Scheduled': 'bg-primary',
                'Completed': 'bg-success',
                'Cancelled': 'bg-danger',
                'In Progress': 'bg-warning'
            }[appointment.status] || 'bg-secondary';

            html += `
                <tr>
                    <td>${appointment.time}</td>
                    <td>${appointment.studentName}</td>
                    <td>${appointment.studentMatric}</td>
                    <td>${appointment.type}</td>
                    <td><span class="badge ${statusClass}">${appointment.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="viewAppointment('${appointment.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success me-1" onclick="startConsultation('${appointment.id}')">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="rescheduleAppointment('${appointment.id}')">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    }

    html += '</tbody></table></div>';
    appointmentsContainer.innerHTML = html;
}

function loadPatientsData() {
    const patientsContainer = document.getElementById('patientsContent');
    if (!patientsContainer) return;

    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};

    let html = `
        <h3 class="mb-4">My Patients</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" placeholder="Search patients..." id="patientSearch" onkeyup="filterPatients()">
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-success" onclick="addPatientRecord()">
                    <i class="fas fa-plus me-2"></i>Add Patient Record
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Matric Number</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Blood Group</th>
                        <th>Last Visit</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    if (Object.keys(registeredPatients).length === 0) {
        html += '<tr><td colspan="6" class="text-center">No patients found</td></tr>';
    } else {
        Object.keys(registeredPatients).forEach(matricNumber => {
            const student = registeredPatients[matricNumber];
            const lastVisit = getRandomDate();

            html += `
                <tr>
                    <td><strong>${matricNumber}</strong></td>
                    <td>${student.name}</td>
                    <td>${student.department || 'N/A'}</td>
                    <td>${student.bloodGroup || 'N/A'}</td>
                    <td>${lastVisit}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="viewPatientRecord('${matricNumber}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success me-1" onclick="addConsultation('${matricNumber}')">
                            <i class="fas fa-notes-medical"></i>
                        </button>
                        <button class="btn btn-sm btn-info me-1" onclick="writePrescriptionFor('${matricNumber}')">
                            <i class="fas fa-prescription"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="orderLabFor('${matricNumber}')">
                            <i class="fas fa-vial"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    }

    html += '</tbody></table></div>';
    patientsContainer.innerHTML = html;
}

function loadScheduleData() {
    const scheduleContainer = document.getElementById('scheduleContent');
    if (!scheduleContainer) return;

    const schedule = generateDoctorSchedule();

    let html = `
        <h3 class="mb-4">My Schedule</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="date" class="form-control" id="scheduleDate" value="${new Date().toISOString().split('T')[0]}" onchange="loadScheduleForDate()">
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="addScheduleSlot()">
                    <i class="fas fa-plus me-2"></i>Add Time Slot
                </button>
            </div>
        </div>
        <div class="row">
    `;

    schedule.forEach(slot => {
        const statusClass = slot.available ? 'border-success' : 'border-warning';
        const statusText = slot.available ? 'Available' : 'Booked';
        const statusIcon = slot.available ? 'fa-check-circle text-success' : 'fa-clock text-warning';

        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card ${statusClass}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${slot.time}</h6>
                                <small class="text-muted">${slot.duration}</small>
                            </div>
                            <div>
                                <i class="fas ${statusIcon}"></i>
                            </div>
                        </div>
                        ${slot.patient ? `<p class="mb-1 mt-2"><strong>${slot.patient}</strong></p>` : ''}
                        ${slot.type ? `<small class="text-muted">${slot.type}</small>` : ''}
                        <div class="mt-2">
                            ${slot.available ?
                                `<button class="btn btn-sm btn-outline-primary" onclick="blockTimeSlot('${slot.id}')">Block</button>` :
                                `<button class="btn btn-sm btn-outline-success" onclick="viewAppointmentDetails('${slot.id}')">View</button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    scheduleContainer.innerHTML = html;
}

function loadPrescriptionsData() {
    const prescriptionsContainer = document.getElementById('prescriptionsContent');
    if (!prescriptionsContainer) return;

    const prescriptions = generateDoctorPrescriptions();

    let html = `
        <h3 class="mb-4">Prescriptions</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="prescriptionFilter" onchange="filterPrescriptions()">
                    <option value="">All Prescriptions</option>
                    <option value="recent">Recent (Last 7 days)</option>
                    <option value="pending">Pending</option>
                    <option value="dispensed">Dispensed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="writePrescription()">
                    <i class="fas fa-plus me-2"></i>Write Prescription
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped" id="prescriptionsTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Student</th>
                        <th>Medication</th>
                        <th>Dosage</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="prescriptionsTableBody">
    `;

    prescriptions.forEach(prescription => {
        const statusClass = {
            'Pending': 'bg-warning',
            'Dispensed': 'bg-success',
            'Cancelled': 'bg-danger'
        }[prescription.status] || 'bg-secondary';

        html += `
            <tr data-status="${prescription.status.toLowerCase()}" data-date="${prescription.date}">
                <td>${prescription.date}</td>
                <td>${prescription.studentName}</td>
                <td>${prescription.medication}</td>
                <td>${prescription.dosage}</td>
                <td><span class="badge ${statusClass}">${prescription.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewPrescriptionDetails('${prescription.id}')" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-info me-1" onclick="editPrescriptionDetails('${prescription.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printPrescriptionDetails('${prescription.id}')" title="Print">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    prescriptionsContainer.innerHTML = html;

    // Store prescriptions data globally for filtering
    window.currentPrescriptions = prescriptions;
}

function loadLabOrdersData() {
    const labOrdersContainer = document.getElementById('labOrdersContent');
    if (!labOrdersContainer) return;

    const labOrders = generateDoctorLabOrders();

    let html = `
        <h3 class="mb-4">Lab Orders</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="labOrderFilter">
                    <option value="">All Lab Orders</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="orderLabTest()">
                    <i class="fas fa-plus me-2"></i>Order Lab Test
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Order Date</th>
                        <th>Student</th>
                        <th>Test Type</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    labOrders.forEach(order => {
        const statusClass = {
            'Pending': 'bg-warning',
            'Completed': 'bg-success',
            'In Progress': 'bg-info',
            'Cancelled': 'bg-danger'
        }[order.status] || 'bg-secondary';

        const priorityClass = {
            'High': 'text-danger',
            'Medium': 'text-warning',
            'Low': 'text-success'
        }[order.priority] || 'text-muted';

        html += `
            <tr>
                <td>${order.date}</td>
                <td>${order.studentName}</td>
                <td>${order.testType}</td>
                <td><span class="${priorityClass}">${order.priority}</span></td>
                <td><span class="badge ${statusClass}">${order.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewLabOrder('${order.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${order.status === 'Completed' ?
                        `<button class="btn btn-sm btn-success me-1" onclick="viewLabResults('${order.id}')">
                            <i class="fas fa-file-medical"></i>
                        </button>` : ''
                    }
                    <button class="btn btn-sm btn-info" onclick="trackLabOrder('${order.id}')">
                        <i class="fas fa-search"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    labOrdersContainer.innerHTML = html;
}

function loadReportsData() {
    const reportsContainer = document.getElementById('reportsContent');
    if (!reportsContainer) return;

    const stats = getDoctorStats();

    let html = `
        <h3 class="mb-4">Reports & Analytics</h3>
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>${stats.todayAppointments}</h3>
                        <p class="mb-0">Today's Appointments</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>${stats.totalPatients}</h3>
                        <p class="mb-0">Total Patients</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>${stats.pendingLabs}</h3>
                        <p class="mb-0">Pending Labs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>${stats.unreadMessages}</h3>
                        <p class="mb-0">Unread Messages</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Export Reports</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportPatientReport()">
                                <i class="fas fa-users me-2"></i>Patient Report
                            </button>
                            <button class="btn btn-outline-success" onclick="exportAppointmentReport()">
                                <i class="fas fa-calendar me-2"></i>Appointment Report
                            </button>
                            <button class="btn btn-outline-info" onclick="exportPrescriptionReport()">
                                <i class="fas fa-prescription me-2"></i>Prescription Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Performance Metrics</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Patient Satisfaction</label>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 92%">92%</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Appointment Completion Rate</label>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 88%">88%</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Response Time</label>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 75%">75%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    reportsContainer.innerHTML = html;
}

function loadMessagesData() {
    const messagesContainer = document.getElementById('messagesContent');
    if (!messagesContainer) return;

    const messages = generateDoctorMessages();

    let html = `
        <h3 class="mb-4">Messages</h3>
        <div class="row mb-3">
            <div class="col-md-6">
                <select class="form-select" id="messageFilter" onchange="filterMessages()">
                    <option value="">All Messages</option>
                    <option value="unread">Unread</option>
                    <option value="patients">From Patients</option>
                    <option value="admin">From Admin</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" onclick="composeNewMessage()">
                    <i class="fas fa-plus me-2"></i>Compose Message
                </button>
            </div>
        </div>
        <div class="list-group" id="messagesList">
    `;

    messages.forEach(message => {
        const unreadClass = message.unread ? 'list-group-item-primary' : '';
        const unreadIcon = message.unread ? '<i class="fas fa-circle text-primary me-2" style="font-size: 8px;"></i>' : '';
        const messageType = message.sender.includes('Admin') ? 'admin' : 'patients';

        html += `
            <div class="list-group-item ${unreadClass}" data-type="${messageType}" data-unread="${message.unread}">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${unreadIcon}${message.subject}</h6>
                    <small>${message.time}</small>
                </div>
                <p class="mb-1">${message.preview}</p>
                <small>From: ${message.sender}</small>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="readMessageDetails('${message.id}')">
                        <i class="fas fa-eye"></i> Read
                    </button>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="replyToMessage('${message.id}')">
                        <i class="fas fa-reply"></i> Reply
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteMessageConfirm('${message.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    });

    html += '</div>';
    messagesContainer.innerHTML = html;

    // Store messages data globally for filtering
    window.currentMessages = messages;
}

// Data generation functions
function generateDoctorAppointments(studentMatrics) {
    const appointments = [];
    const appointmentTypes = ['General Checkup', 'Follow-up', 'Consultation', 'Emergency', 'Routine Exam'];
    const statuses = ['Scheduled', 'Completed', 'In Progress'];
    const times = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30'];

    for (let i = 0; i < Math.min(8, studentMatrics.length); i++) {
        const randomStudent = studentMatrics[Math.floor(Math.random() * studentMatrics.length)];
        if (randomStudent) {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            appointments.push({
                id: `APT${String(i + 1).padStart(3, '0')}`,
                time: times[i % times.length],
                studentName: registeredPatients[randomStudent]?.name || 'Unknown Student',
                studentMatric: randomStudent,
                type: appointmentTypes[Math.floor(Math.random() * appointmentTypes.length)],
                status: statuses[Math.floor(Math.random() * statuses.length)]
            });
        }
    }

    return appointments;
}

function generateDoctorSchedule() {
    const schedule = [];
    const times = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'];
    const patients = ['John Doe', 'Jane Smith', 'Ahmed Ali', 'Fatima Hassan', 'Michael Brown'];
    const types = ['Consultation', 'Follow-up', 'Checkup', 'Emergency'];

    times.forEach((time, index) => {
        const available = Math.random() > 0.4; // 60% chance of being booked
        schedule.push({
            id: `SLOT${index + 1}`,
            time: time,
            duration: '30 min',
            available: available,
            patient: available ? null : patients[Math.floor(Math.random() * patients.length)],
            type: available ? null : types[Math.floor(Math.random() * types.length)]
        });
    });

    return schedule;
}

function generateDoctorPrescriptions() {
    const prescriptions = [];
    const medications = ['Paracetamol', 'Amoxicillin', 'Ibuprofen', 'Metformin', 'Lisinopril', 'Omeprazole'];
    const dosages = ['500mg twice daily', '250mg three times daily', '200mg once daily', '100mg twice daily'];
    const statuses = ['Pending', 'Dispensed', 'Cancelled'];
    const students = ['Ahmed Musa', 'Fatima Ibrahim', 'John Adamu', 'Aisha Bello', 'Mohammed Sani'];

    for (let i = 0; i < 6; i++) {
        prescriptions.push({
            id: `PRESC${String(i + 1).padStart(3, '0')}`,
            date: getRandomDate(),
            studentName: students[Math.floor(Math.random() * students.length)],
            medication: medications[Math.floor(Math.random() * medications.length)],
            dosage: dosages[Math.floor(Math.random() * dosages.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)]
        });
    }

    return prescriptions;
}

function generateDoctorLabOrders() {
    const labOrders = [];
    const testTypes = ['Blood Test', 'Urine Analysis', 'X-Ray', 'ECG', 'Blood Sugar', 'Cholesterol Test'];
    const priorities = ['High', 'Medium', 'Low'];
    const statuses = ['Pending', 'In Progress', 'Completed', 'Cancelled'];
    const students = ['Ahmed Musa', 'Fatima Ibrahim', 'John Adamu', 'Aisha Bello', 'Mohammed Sani'];

    for (let i = 0; i < 5; i++) {
        labOrders.push({
            id: `LAB${String(i + 1).padStart(3, '0')}`,
            date: getRandomDate(),
            studentName: students[Math.floor(Math.random() * students.length)],
            testType: testTypes[Math.floor(Math.random() * testTypes.length)],
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)]
        });
    }

    return labOrders;
}

function generateDoctorMessages() {
    const messages = [];
    const subjects = ['Appointment Request', 'Lab Results Query', 'Prescription Refill', 'Medical Certificate', 'Follow-up Question'];
    const senders = ['Ahmed Musa (22A/UE/BSE/1001)', 'Fatima Ibrahim (22B/UE/BNS/1001)', 'Admin Office', 'Lab Department', 'Pharmacy'];
    const previews = [
        'I would like to schedule an appointment for next week...',
        'Could you please review my recent lab results...',
        'I need a refill for my prescription...',
        'I need a medical certificate for my absence...',
        'I have a question about my treatment plan...'
    ];

    for (let i = 0; i < 5; i++) {
        messages.push({
            id: `MSG${String(i + 1).padStart(3, '0')}`,
            subject: subjects[Math.floor(Math.random() * subjects.length)],
            sender: senders[Math.floor(Math.random() * senders.length)],
            preview: previews[Math.floor(Math.random() * previews.length)],
            time: getRandomTime(),
            unread: Math.random() > 0.5
        });
    }

    return messages;
}

function getRandomDate() {
    const start = new Date();
    start.setDate(start.getDate() - 30);
    const end = new Date();
    const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return randomDate.toLocaleDateString();
}

function getRandomTime() {
    const hours = Math.floor(Math.random() * 24);
    const minutes = Math.floor(Math.random() * 60);
    const now = new Date();
    now.setHours(hours, minutes);
    return now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

// Modal functions
function showAddPatientModal() {
    const modalHtml = `
        <div class="modal fade" id="addPatientModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Patient Record</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addPatientForm">
                            <div class="mb-3">
                                <label class="form-label">Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Consultation Notes</label>
                                <textarea class="form-control" name="notes" rows="4" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Diagnosis</label>
                                <input type="text" class="form-control" name="diagnosis" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="savePatientRecord()">Save Record</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('addPatientModal'));
    modal.show();
}

function showPrescriptionModal() {
    const modalHtml = `
        <div class="modal fade" id="prescriptionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-prescription me-2"></i>Write Prescription</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="prescriptionForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Patient Matric Number</label>
                                        <input type="text" class="form-control" name="matricNumber" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Date</label>
                                        <input type="date" class="form-control" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Medication</label>
                                <input type="text" class="form-control" name="medication" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Dosage</label>
                                        <input type="text" class="form-control" name="dosage" placeholder="e.g., 500mg twice daily" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Duration</label>
                                        <input type="text" class="form-control" name="duration" placeholder="e.g., 7 days" required>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Instructions</label>
                                <textarea class="form-control" name="instructions" rows="3" placeholder="Special instructions for the patient"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="savePrescription()">Save Prescription</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('prescriptionModal'));
    modal.show();
}

function showLabOrderModal() {
    const modalHtml = `
        <div class="modal fade" id="labOrderModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title"><i class="fas fa-vial me-2"></i>Order Lab Test</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="labOrderForm">
                            <div class="mb-3">
                                <label class="form-label">Patient Matric Number</label>
                                <input type="text" class="form-control" name="matricNumber" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Test Type</label>
                                <select class="form-select" name="testType" required>
                                    <option value="">Select Test Type</option>
                                    <option value="Blood Test">Blood Test</option>
                                    <option value="Urine Analysis">Urine Analysis</option>
                                    <option value="X-Ray">X-Ray</option>
                                    <option value="ECG">ECG</option>
                                    <option value="Blood Sugar">Blood Sugar</option>
                                    <option value="Cholesterol Test">Cholesterol Test</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <select class="form-select" name="priority" required>
                                    <option value="Low">Low</option>
                                    <option value="Medium" selected>Medium</option>
                                    <option value="High">High</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Clinical Notes</label>
                                <textarea class="form-control" name="notes" rows="3" placeholder="Reason for test and clinical notes"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="saveLabOrder()">Order Test</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('labOrderModal'));
    modal.show();
}

// Action handlers
function savePatientRecord() {
    showSuccessAlert('Patient record added successfully!');
    bootstrap.Modal.getInstance(document.getElementById('addPatientModal')).hide();
    loadPatientsData();
}

function savePrescription() {
    showSuccessAlert('Prescription saved successfully!');
    bootstrap.Modal.getInstance(document.getElementById('prescriptionModal')).hide();
    loadPrescriptionsData();
}

function saveLabOrder() {
    showSuccessAlert('Lab test ordered successfully!');
    bootstrap.Modal.getInstance(document.getElementById('labOrderModal')).hide();
    loadLabOrdersData();
}

// Additional action functions
function viewAppointment(id) { showSuccessAlert(`Viewing appointment ${id}`); }
function startConsultation(id) { showSuccessAlert(`Starting consultation for appointment ${id}`); }
function rescheduleAppointment(id) { showSuccessAlert(`Rescheduling appointment ${id}`); }
function viewPatientRecord(matricNumber) { showSuccessAlert(`Viewing record for ${matricNumber}`); }
function addConsultation(matricNumber) { showSuccessAlert(`Adding consultation for ${matricNumber}`); }
function writePrescriptionFor(matricNumber) { showPrescriptionModal(); }
function orderLabFor(matricNumber) { showLabOrderModal(); }
// These functions are now replaced by the enhanced versions above
function viewLabOrder(id) { showSuccessAlert(`Viewing lab order ${id}`); }
function viewLabResults(id) { showSuccessAlert(`Viewing lab results for ${id}`); }
function trackLabOrder(id) { showSuccessAlert(`Tracking lab order ${id}`); }
// These functions are now replaced by the enhanced versions above
function exportPatientReport() { showSuccessAlert('Patient report exported!'); }
function exportAppointmentReport() { showSuccessAlert('Appointment report exported!'); }
function exportPrescriptionReport() { showSuccessAlert('Prescription report exported!'); }

// Prescription filtering function
function filterPrescriptions() {
    const filter = document.getElementById('prescriptionFilter').value;
    const tableBody = document.getElementById('prescriptionsTableBody');
    const rows = tableBody.querySelectorAll('tr');

    rows.forEach(row => {
        const status = row.getAttribute('data-status');
        const date = new Date(row.getAttribute('data-date'));
        const now = new Date();
        const daysDiff = Math.floor((now - date) / (1000 * 60 * 60 * 24));

        let showRow = true;

        switch(filter) {
            case 'recent':
                showRow = daysDiff <= 7;
                break;
            case 'pending':
                showRow = status === 'pending';
                break;
            case 'dispensed':
                showRow = status === 'dispensed';
                break;
            case 'cancelled':
                showRow = status === 'cancelled';
                break;
            default:
                showRow = true;
        }

        row.style.display = showRow ? '' : 'none';
    });
}

// Enhanced prescription management functions
function viewPrescriptionDetails(prescriptionId) {
    const prescription = window.currentPrescriptions?.find(p => p.id === prescriptionId);
    if (!prescription) {
        showErrorAlert('Prescription not found!');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="viewPrescriptionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Prescription Details</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Patient Information</h6>
                                <p><strong>Name:</strong> ${prescription.studentName}</p>
                                <p><strong>Date:</strong> ${prescription.date}</p>
                                <p><strong>Prescription ID:</strong> ${prescription.id}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Doctor Information</h6>
                                <p><strong>Doctor:</strong> ${currentDoctor?.name || 'Dr. Ahmed Musa'}</p>
                                <p><strong>Specialty:</strong> ${currentDoctor?.specialty || 'General Medicine'}</p>
                                <p><strong>License:</strong> ${currentDoctor?.licenseNumber || 'MD-001-2020'}</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-8">
                                <h6>Medication Details</h6>
                                <p><strong>Medication:</strong> ${prescription.medication}</p>
                                <p><strong>Dosage:</strong> ${prescription.dosage}</p>
                                <p><strong>Duration:</strong> 7 days</p>
                            </div>
                            <div class="col-md-4">
                                <h6>Status</h6>
                                <span class="badge bg-${prescription.status === 'Pending' ? 'warning' : prescription.status === 'Dispensed' ? 'success' : 'danger'} fs-6">${prescription.status}</span>
                            </div>
                        </div>
                        <hr>
                        <div>
                            <h6>Instructions</h6>
                            <p>Take medication as prescribed. Complete the full course even if symptoms improve. Contact doctor if side effects occur.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-info" onclick="editPrescriptionDetails('${prescription.id}')">
                            <i class="fas fa-edit me-2"></i>Edit
                        </button>
                        <button type="button" class="btn btn-success" onclick="printPrescriptionDetails('${prescription.id}')">
                            <i class="fas fa-print me-2"></i>Print
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('viewPrescriptionModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('viewPrescriptionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function editPrescriptionDetails(prescriptionId) {
    const prescription = window.currentPrescriptions?.find(p => p.id === prescriptionId);
    if (!prescription) {
        showErrorAlert('Prescription not found!');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="editPrescriptionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Prescription</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editPrescriptionForm">
                            <input type="hidden" name="prescriptionId" value="${prescription.id}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Patient Name</label>
                                        <input type="text" class="form-control" value="${prescription.studentName}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Date</label>
                                        <input type="date" class="form-control" name="date" value="${new Date().toISOString().split('T')[0]}">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Medication</label>
                                <input type="text" class="form-control" name="medication" value="${prescription.medication}" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Dosage</label>
                                        <input type="text" class="form-control" name="dosage" value="${prescription.dosage}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select class="form-select" name="status" required>
                                            <option value="Pending" ${prescription.status === 'Pending' ? 'selected' : ''}>Pending</option>
                                            <option value="Dispensed" ${prescription.status === 'Dispensed' ? 'selected' : ''}>Dispensed</option>
                                            <option value="Cancelled" ${prescription.status === 'Cancelled' ? 'selected' : ''}>Cancelled</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Instructions</label>
                                <textarea class="form-control" name="instructions" rows="3">Take medication as prescribed. Complete the full course even if symptoms improve.</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-info" onclick="updatePrescription('${prescription.id}')">
                            <i class="fas fa-save me-2"></i>Update Prescription
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editPrescriptionModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('editPrescriptionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function printPrescriptionDetails(prescriptionId) {
    const prescription = window.currentPrescriptions?.find(p => p.id === prescriptionId);
    if (!prescription) {
        showErrorAlert('Prescription not found!');
        return;
    }

    const printWindow = window.open('', '_blank');
    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Prescription - ${prescription.id}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #007BFF; padding-bottom: 10px; margin-bottom: 20px; }
                .prescription-details { margin: 20px 0; }
                .medication-box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Newgate Hospital Management System</h2>
                <p>Minna-Bida Road, Minna, Niger State | Phone: +234 ************</p>
            </div>

            <div class="prescription-details">
                <div style="display: flex; justify-content: space-between;">
                    <div>
                        <h4>Patient Information</h4>
                        <p><strong>Name:</strong> ${prescription.studentName}</p>
                        <p><strong>Date:</strong> ${prescription.date}</p>
                    </div>
                    <div>
                        <h4>Doctor Information</h4>
                        <p><strong>Dr.:</strong> ${currentDoctor?.name || 'Dr. Ahmed Musa'}</p>
                        <p><strong>License:</strong> ${currentDoctor?.licenseNumber || 'MD-001-2020'}</p>
                    </div>
                </div>

                <div class="medication-box">
                    <h4>Prescription Details</h4>
                    <p><strong>Medication:</strong> ${prescription.medication}</p>
                    <p><strong>Dosage:</strong> ${prescription.dosage}</p>
                    <p><strong>Duration:</strong> 7 days</p>
                    <p><strong>Instructions:</strong> Take medication as prescribed. Complete the full course even if symptoms improve.</p>
                </div>

                <div style="margin-top: 30px;">
                    <p><strong>Prescription ID:</strong> ${prescription.id}</p>
                    <p><strong>Status:</strong> ${prescription.status}</p>
                </div>
            </div>

            <div class="footer">
                <p>This prescription is valid for 30 days from the date of issue.</p>
                <p>For any queries, contact: +234 ************</p>
            </div>

            <div class="no-print" style="text-align: center; margin-top: 20px;">
                <button onclick="window.print()" style="padding: 10px 20px; background: #007BFF; color: white; border: none; border-radius: 5px;">Print Prescription</button>
                <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; margin-left: 10px;">Close</button>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    showSuccessAlert('Prescription opened in new window for printing!');
}

function updatePrescription(prescriptionId) {
    const form = document.getElementById('editPrescriptionForm');
    const formData = new FormData(form);

    // Update the prescription in the current data
    const prescriptionIndex = window.currentPrescriptions?.findIndex(p => p.id === prescriptionId);
    if (prescriptionIndex !== -1) {
        window.currentPrescriptions[prescriptionIndex].medication = formData.get('medication');
        window.currentPrescriptions[prescriptionIndex].dosage = formData.get('dosage');
        window.currentPrescriptions[prescriptionIndex].status = formData.get('status');
        window.currentPrescriptions[prescriptionIndex].date = formData.get('date');
    }

    showSuccessAlert('Prescription updated successfully!');
    bootstrap.Modal.getInstance(document.getElementById('editPrescriptionModal')).hide();
    loadPrescriptionsData(); // Reload the prescriptions table
}

// Enhanced message functions
function filterMessages() {
    const filter = document.getElementById('messageFilter').value;
    const messagesList = document.getElementById('messagesList');
    const messages = messagesList.querySelectorAll('.list-group-item');

    messages.forEach(message => {
        const messageType = message.getAttribute('data-type');
        const isUnread = message.getAttribute('data-unread') === 'true';

        let showMessage = true;

        switch(filter) {
            case 'unread':
                showMessage = isUnread;
                break;
            case 'patients':
                showMessage = messageType === 'patients';
                break;
            case 'admin':
                showMessage = messageType === 'admin';
                break;
            default:
                showMessage = true;
        }

        message.style.display = showMessage ? '' : 'none';
    });
}

function readMessageDetails(messageId) {
    const message = window.currentMessages?.find(m => m.id === messageId);
    if (!message) {
        showErrorAlert('Message not found!');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="readMessageModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-envelope-open me-2"></i>${message.subject}</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>From:</strong> ${message.sender}
                            </div>
                            <div class="col-md-6 text-end">
                                <strong>Time:</strong> ${message.time}
                            </div>
                        </div>
                        <hr>
                        <div class="message-content">
                            <p>${message.preview}</p>
                            <p>Dear Dr. ${currentDoctor?.name || 'Ahmed Musa'},</p>
                            <p>I hope this message finds you well. I am writing to request your assistance with the following matter:</p>
                            <p>${getFullMessageContent(message.subject)}</p>
                            <p>I would greatly appreciate your guidance on this matter. Please let me know if you need any additional information.</p>
                            <p>Thank you for your time and consideration.</p>
                            <p>Best regards,<br>${message.sender}</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" onclick="replyToMessage('${message.id}')">
                            <i class="fas fa-reply me-2"></i>Reply
                        </button>
                        <button type="button" class="btn btn-info" onclick="forwardMessage('${message.id}')">
                            <i class="fas fa-share me-2"></i>Forward
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('readMessageModal'));
    modal.show();

    // Mark message as read
    markMessageAsRead(messageId);

    // Remove modal from DOM when closed
    document.getElementById('readMessageModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function replyToMessage(messageId) {
    const message = window.currentMessages?.find(m => m.id === messageId);
    if (!message) {
        showErrorAlert('Message not found!');
        return;
    }

    // Close read modal if open
    const readModal = document.getElementById('readMessageModal');
    if (readModal) {
        bootstrap.Modal.getInstance(readModal)?.hide();
    }

    const modalHtml = `
        <div class="modal fade" id="replyMessageModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-reply me-2"></i>Reply to Message</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="replyMessageForm">
                            <input type="hidden" name="originalMessageId" value="${message.id}">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">To:</label>
                                    <input type="text" class="form-control" value="${message.sender}" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Subject:</label>
                                    <input type="text" class="form-control" name="subject" value="Re: ${message.subject}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Message:</label>
                                <textarea class="form-control" name="message" rows="8" required placeholder="Type your reply here...">Dear ${message.sender.split(' ')[0]},

Thank you for your message regarding "${message.subject}".



Best regards,
Dr. ${currentDoctor?.name || 'Ahmed Musa'}
${currentDoctor?.specialty || 'General Medicine'}
Newgate Hospital Management System</textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority:</label>
                                <select class="form-select" name="priority">
                                    <option value="normal">Normal</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </form>

                        <div class="mt-3">
                            <h6>Original Message:</h6>
                            <div class="border rounded p-3 bg-light">
                                <small><strong>From:</strong> ${message.sender} | <strong>Time:</strong> ${message.time}</small>
                                <p class="mt-2 mb-0">${message.preview}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveDraft('${message.id}')">
                            <i class="fas fa-save me-2"></i>Save Draft
                        </button>
                        <button type="button" class="btn btn-success" onclick="sendReply('${message.id}')">
                            <i class="fas fa-paper-plane me-2"></i>Send Reply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('replyMessageModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('replyMessageModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function composeNewMessage() {
    const modalHtml = `
        <div class="modal fade" id="composeMessageModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Compose New Message</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="composeMessageForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">To:</label>
                                    <select class="form-select" name="recipient" required>
                                        <option value="">Select Recipient</option>
                                        <option value="admin">Hospital Administration</option>
                                        <option value="pharmacy">Pharmacy Department</option>
                                        <option value="lab">Laboratory Department</option>
                                        <option value="nursing">Nursing Department</option>
                                        <option value="patient">Specific Patient</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Priority:</label>
                                    <select class="form-select" name="priority">
                                        <option value="normal">Normal</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Subject:</label>
                                <input type="text" class="form-control" name="subject" required placeholder="Enter message subject">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Message:</label>
                                <textarea class="form-control" name="message" rows="8" required placeholder="Type your message here...">Dear Colleague,



Best regards,
Dr. ${currentDoctor?.name || 'Ahmed Musa'}
${currentDoctor?.specialty || 'General Medicine'}
Newgate Hospital Management System</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveMessageDraft()">
                            <i class="fas fa-save me-2"></i>Save Draft
                        </button>
                        <button type="button" class="btn btn-success" onclick="sendNewMessage()">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('composeMessageModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('composeMessageModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function sendReply(messageId) {
    const form = document.getElementById('replyMessageForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    // Simulate sending reply
    showSuccessAlert('Reply sent successfully!');
    bootstrap.Modal.getInstance(document.getElementById('replyMessageModal')).hide();

    // Update message count
    loadDashboardData();
}

function sendNewMessage() {
    const form = document.getElementById('composeMessageForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    // Simulate sending message
    showSuccessAlert('Message sent successfully!');
    bootstrap.Modal.getInstance(document.getElementById('composeMessageModal')).hide();
}

function saveDraft(messageId) {
    showSuccessAlert('Draft saved successfully!');
}

function saveMessageDraft() {
    showSuccessAlert('Draft saved successfully!');
}

function markMessageAsRead(messageId) {
    const messageIndex = window.currentMessages?.findIndex(m => m.id === messageId);
    if (messageIndex !== -1) {
        window.currentMessages[messageIndex].unread = false;
    }
    loadMessagesData(); // Reload messages to update UI
}

function deleteMessageConfirm(messageId) {
    if (confirm('Are you sure you want to delete this message?')) {
        showSuccessAlert('Message deleted successfully!');
        loadMessagesData(); // Reload messages
    }
}

function forwardMessage(messageId) {
    showSuccessAlert('Forward message feature coming soon!');
}

function getFullMessageContent(subject) {
    const contentMap = {
        'Appointment Request': 'I would like to schedule an appointment for a routine checkup. I am available next week on Tuesday or Wednesday afternoon. Please let me know what times are available.',
        'Lab Results Query': 'I recently had blood work done and wanted to follow up on the results. Could you please review them and let me know if there are any concerns or recommendations?',
        'Prescription Refill': 'I am running low on my current medication and would like to request a refill. The prescription was for Paracetamol 500mg, and I have been taking it as directed.',
        'Medical Certificate': 'I need a medical certificate for my recent absence from classes due to illness. I was under your care last week for flu symptoms.',
        'Follow-up Question': 'I have a question about the treatment plan we discussed during my last visit. I wanted to clarify the dosage instructions for my medication.'
    };

    return contentMap[subject] || 'I have a medical inquiry that requires your professional attention and guidance.';
}

// Doctor Profile and Settings Functions
function showDoctorProfileModal() {
    if (!currentDoctor) {
        showErrorAlert('Doctor information not available!');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="doctorProfileModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-user-md me-2"></i>Doctor Profile</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="profile-image-container mb-3">
                                    <img src="https://cdn-icons-png.flaticon.com/128/2785/2785490.png" alt="Doctor Avatar" class="rounded-circle" style="width: 120px; height: 120px; border: 3px solid #007BFF;">
                                </div>
                                <button class="btn btn-outline-primary btn-sm" onclick="changeProfilePicture()">
                                    <i class="fas fa-camera me-2"></i>Change Photo
                                </button>
                            </div>
                            <div class="col-md-8">
                                <form id="doctorProfileForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Doctor ID</label>
                                                <input type="text" class="form-control" value="${currentDoctor.id}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Full Name</label>
                                                <input type="text" class="form-control" name="name" value="${currentDoctor.name}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Email</label>
                                                <input type="email" class="form-control" name="email" value="${currentDoctor.email}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Phone</label>
                                                <input type="tel" class="form-control" name="phone" value="${currentDoctor.phone}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Specialty</label>
                                                <select class="form-select" name="specialty" required>
                                                    <option value="General Medicine" ${currentDoctor.specialty === 'General Medicine' ? 'selected' : ''}>General Medicine</option>
                                                    <option value="Pediatrics" ${currentDoctor.specialty === 'Pediatrics' ? 'selected' : ''}>Pediatrics</option>
                                                    <option value="Cardiology" ${currentDoctor.specialty === 'Cardiology' ? 'selected' : ''}>Cardiology</option>
                                                    <option value="Dermatology" ${currentDoctor.specialty === 'Dermatology' ? 'selected' : ''}>Dermatology</option>
                                                    <option value="Orthopedics" ${currentDoctor.specialty === 'Orthopedics' ? 'selected' : ''}>Orthopedics</option>
                                                    <option value="Neurology" ${currentDoctor.specialty === 'Neurology' ? 'selected' : ''}>Neurology</option>
                                                    <option value="Psychiatry" ${currentDoctor.specialty === 'Psychiatry' ? 'selected' : ''}>Psychiatry</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Department</label>
                                                <input type="text" class="form-control" name="department" value="${currentDoctor.department}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">License Number</label>
                                                <input type="text" class="form-control" name="licenseNumber" value="${currentDoctor.licenseNumber}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Experience</label>
                                                <input type="text" class="form-control" name="experience" value="${currentDoctor.experience}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Education</label>
                                        <input type="text" class="form-control" name="education" value="${currentDoctor.education}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Bio/About</label>
                                        <textarea class="form-control" name="bio" rows="3" placeholder="Brief professional biography...">${currentDoctor.bio || 'Dedicated medical professional committed to providing excellent patient care and advancing healthcare standards.'}</textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-line me-2"></i>Professional Statistics</h6>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="card bg-primary text-white text-center">
                                            <div class="card-body p-2">
                                                <h6 class="mb-1">${getDoctorStats().totalPatients}</h6>
                                                <small>Total Patients</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="card bg-success text-white text-center">
                                            <div class="card-body p-2">
                                                <h6 class="mb-1">${getDoctorStats().todayAppointments}</h6>
                                                <small>Today's Appointments</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock me-2"></i>Account Information</h6>
                                <p class="mb-1"><strong>Last Login:</strong> ${currentDoctor.lastLogin ? new Date(currentDoctor.lastLogin).toLocaleString() : 'First time login'}</p>
                                <p class="mb-1"><strong>Account Status:</strong> <span class="badge bg-success">Active</span></p>
                                <p class="mb-1"><strong>Role:</strong> Doctor</p>
                                <p class="mb-0"><strong>Member Since:</strong> ${new Date().getFullYear() - 2} years</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="changePassword()">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                        <button type="button" class="btn btn-primary" onclick="updateDoctorProfile()">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('doctorProfileModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('doctorProfileModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function showDoctorSettingsModal() {
    const modalHtml = `
        <div class="modal fade" id="doctorSettingsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title"><i class="fas fa-cog me-2"></i>Doctor Settings</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-bell me-2"></i>Notification Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="notificationSettings">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                                <label class="form-check-label" for="emailNotifications">
                                                    Email Notifications
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="appointmentReminders" checked>
                                                <label class="form-check-label" for="appointmentReminders">
                                                    Appointment Reminders
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="labResultAlerts" checked>
                                                <label class="form-check-label" for="labResultAlerts">
                                                    Lab Result Alerts
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="messageNotifications" checked>
                                                <label class="form-check-label" for="messageNotifications">
                                                    New Message Notifications
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="emergencyAlerts" checked>
                                                <label class="form-check-label" for="emergencyAlerts">
                                                    Emergency Alerts
                                                </label>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-desktop me-2"></i>Dashboard Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="dashboardSettings">
                                            <div class="mb-3">
                                                <label class="form-label">Default View</label>
                                                <select class="form-select" name="defaultView">
                                                    <option value="dashboard">Dashboard</option>
                                                    <option value="appointments">Appointments</option>
                                                    <option value="patients">My Patients</option>
                                                    <option value="schedule">Schedule</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Items per Page</label>
                                                <select class="form-select" name="itemsPerPage">
                                                    <option value="10">10 items</option>
                                                    <option value="25" selected>25 items</option>
                                                    <option value="50">50 items</option>
                                                    <option value="100">100 items</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Theme</label>
                                                <select class="form-select" name="theme">
                                                    <option value="light" selected>Light Theme</option>
                                                    <option value="dark">Dark Theme</option>
                                                    <option value="auto">Auto (System)</option>
                                                </select>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                                <label class="form-check-label" for="autoRefresh">
                                                    Auto-refresh Data
                                                </label>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="compactView">
                                                <label class="form-check-label" for="compactView">
                                                    Compact View
                                                </label>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">Session Timeout</label>
                                            <select class="form-select" name="sessionTimeout">
                                                <option value="30">30 minutes</option>
                                                <option value="60" selected>1 hour</option>
                                                <option value="120">2 hours</option>
                                                <option value="240">4 hours</option>
                                            </select>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                            <label class="form-check-label" for="twoFactorAuth">
                                                Two-Factor Authentication
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="loginAlerts" checked>
                                            <label class="form-check-label" for="loginAlerts">
                                                Login Alerts
                                            </label>
                                        </div>
                                        <div class="d-grid">
                                            <button class="btn btn-warning" onclick="changePasswordFromSettings()">
                                                <i class="fas fa-key me-2"></i>Change Password
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Schedule Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">Working Hours Start</label>
                                            <input type="time" class="form-control" name="workStart" value="08:00">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Working Hours End</label>
                                            <input type="time" class="form-control" name="workEnd" value="17:00">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Appointment Duration</label>
                                            <select class="form-select" name="appointmentDuration">
                                                <option value="15">15 minutes</option>
                                                <option value="30" selected>30 minutes</option>
                                                <option value="45">45 minutes</option>
                                                <option value="60">1 hour</option>
                                            </select>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="weekendAvailable">
                                            <label class="form-check-label" for="weekendAvailable">
                                                Available on Weekends
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="resetSettingsToDefault()">
                            <i class="fas fa-undo me-2"></i>Reset to Default
                        </button>
                        <button type="button" class="btn btn-info" onclick="saveDoctorSettings()">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('doctorSettingsModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('doctorSettingsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Profile and Settings Management Functions
function updateDoctorProfile() {
    const form = document.getElementById('doctorProfileForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    // Update current doctor data
    currentDoctor.name = formData.get('name');
    currentDoctor.email = formData.get('email');
    currentDoctor.phone = formData.get('phone');
    currentDoctor.specialty = formData.get('specialty');
    currentDoctor.department = formData.get('department');
    currentDoctor.licenseNumber = formData.get('licenseNumber');
    currentDoctor.experience = formData.get('experience');
    currentDoctor.education = formData.get('education');
    currentDoctor.bio = formData.get('bio');

    // Update session storage
    sessionStorage.setItem('hospitalUser', JSON.stringify(currentDoctor));
    localStorage.setItem('hospitalUser', JSON.stringify(currentDoctor));

    // Update UI
    updateDoctorInfo();

    showSuccessAlert('Profile updated successfully!');
    bootstrap.Modal.getInstance(document.getElementById('doctorProfileModal')).hide();
}

function changePassword() {
    // Close profile modal if open
    const profileModal = document.getElementById('doctorProfileModal');
    if (profileModal) {
        bootstrap.Modal.getInstance(profileModal)?.hide();
    }

    showChangePasswordModal();
}

function changePasswordFromSettings() {
    // Close settings modal if open
    const settingsModal = document.getElementById('doctorSettingsModal');
    if (settingsModal) {
        bootstrap.Modal.getInstance(settingsModal)?.hide();
    }

    showChangePasswordModal();
}

function showChangePasswordModal() {
    const modalHtml = `
        <div class="modal fade" id="changePasswordModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title"><i class="fas fa-key me-2"></i>Change Password</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="changePasswordForm">
                            <div class="mb-3">
                                <label class="form-label">Current Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="currentPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('currentPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">New Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="newPassword" required minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('newPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Password must be at least 6 characters long</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Confirm New Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" name="confirmPassword" required minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('confirmPassword', this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Password Requirements:</h6>
                                <ul class="mb-0 small">
                                    <li>At least 6 characters long</li>
                                    <li>Should contain letters and numbers</li>
                                    <li>Avoid using personal information</li>
                                    <li>Use a unique password for this account</li>
                                </ul>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="updatePassword()">
                            <i class="fas fa-save me-2"></i>Update Password
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();

    // Remove modal from DOM when closed
    document.getElementById('changePasswordModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function updatePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);

    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }

    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const confirmPassword = formData.get('confirmPassword');

    // Validate current password (in real app, this would be server-side)
    if (currentPassword !== 'doctor123') {
        showErrorAlert('Current password is incorrect!');
        return;
    }

    // Validate new password confirmation
    if (newPassword !== confirmPassword) {
        showErrorAlert('New passwords do not match!');
        return;
    }

    // Update password (in real app, this would be server-side)
    currentDoctor.password = newPassword;
    sessionStorage.setItem('hospitalUser', JSON.stringify(currentDoctor));
    localStorage.setItem('hospitalUser', JSON.stringify(currentDoctor));

    showSuccessAlert('Password updated successfully!');
    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
}

function saveDoctorSettings() {
    const notificationSettings = {
        emailNotifications: document.getElementById('emailNotifications').checked,
        appointmentReminders: document.getElementById('appointmentReminders').checked,
        labResultAlerts: document.getElementById('labResultAlerts').checked,
        messageNotifications: document.getElementById('messageNotifications').checked,
        emergencyAlerts: document.getElementById('emergencyAlerts').checked
    };

    const dashboardSettings = {
        defaultView: document.querySelector('[name="defaultView"]').value,
        itemsPerPage: document.querySelector('[name="itemsPerPage"]').value,
        theme: document.querySelector('[name="theme"]').value,
        autoRefresh: document.getElementById('autoRefresh').checked,
        compactView: document.getElementById('compactView').checked
    };

    const securitySettings = {
        sessionTimeout: document.querySelector('[name="sessionTimeout"]').value,
        twoFactorAuth: document.getElementById('twoFactorAuth').checked,
        loginAlerts: document.getElementById('loginAlerts').checked
    };

    const scheduleSettings = {
        workStart: document.querySelector('[name="workStart"]').value,
        workEnd: document.querySelector('[name="workEnd"]').value,
        appointmentDuration: document.querySelector('[name="appointmentDuration"]').value,
        weekendAvailable: document.getElementById('weekendAvailable').checked
    };

    const allSettings = {
        notifications: notificationSettings,
        dashboard: dashboardSettings,
        security: securitySettings,
        schedule: scheduleSettings,
        lastUpdated: new Date().toISOString()
    };

    // Save settings to localStorage
    localStorage.setItem(`doctorSettings_${currentDoctor.id}`, JSON.stringify(allSettings));

    // Apply theme setting
    applyThemeSetting(dashboardSettings.theme);

    showSuccessAlert('Settings saved successfully!');
    bootstrap.Modal.getInstance(document.getElementById('doctorSettingsModal')).hide();
}

function resetSettingsToDefault() {
    if (confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
        // Remove saved settings
        localStorage.removeItem(`doctorSettings_${currentDoctor.id}`);

        // Reset form values to default
        document.getElementById('emailNotifications').checked = true;
        document.getElementById('appointmentReminders').checked = true;
        document.getElementById('labResultAlerts').checked = true;
        document.getElementById('messageNotifications').checked = true;
        document.getElementById('emergencyAlerts').checked = true;

        document.querySelector('[name="defaultView"]').value = 'dashboard';
        document.querySelector('[name="itemsPerPage"]').value = '25';
        document.querySelector('[name="theme"]').value = 'light';
        document.getElementById('autoRefresh').checked = true;
        document.getElementById('compactView').checked = false;

        document.querySelector('[name="sessionTimeout"]').value = '60';
        document.getElementById('twoFactorAuth').checked = false;
        document.getElementById('loginAlerts').checked = true;

        document.querySelector('[name="workStart"]').value = '08:00';
        document.querySelector('[name="workEnd"]').value = '17:00';
        document.querySelector('[name="appointmentDuration"]').value = '30';
        document.getElementById('weekendAvailable').checked = false;

        showSuccessAlert('Settings reset to default values!');
    }
}

function changeProfilePicture() {
    showSuccessAlert('Profile picture change feature coming soon!');
}

function applyThemeSetting(theme) {
    const body = document.body;

    // Remove existing theme classes
    body.classList.remove('theme-light', 'theme-dark');

    if (theme === 'dark') {
        body.classList.add('theme-dark');
    } else if (theme === 'light') {
        body.classList.add('theme-light');
    } else if (theme === 'auto') {
        // Use system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('theme-dark');
        } else {
            body.classList.add('theme-light');
        }
    }
}

function togglePasswordVisibility(inputName, button) {
    const input = document.querySelector(`[name="${inputName}"]`);
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Load saved settings on dashboard initialization
function loadDoctorSettings() {
    if (!currentDoctor) return;

    const savedSettings = localStorage.getItem(`doctorSettings_${currentDoctor.id}`);
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);

        // Apply theme setting
        if (settings.dashboard && settings.dashboard.theme) {
            applyThemeSetting(settings.dashboard.theme);
        }

        // Apply other settings as needed
        console.log('Loaded doctor settings:', settings);
    }
}
