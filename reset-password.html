<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-hospital me-2"></i>Newgate Hospital
            </a>
        </div>
    </nav>

    <!-- Reset Password Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <h3 class="mb-0">
                                <i class="fas fa-key me-2"></i>Reset Password
                            </h3>
                            <p class="mb-0 mt-2 opacity-75">Create a new password for your account</p>
                        </div>
                        
                        <div class="card-body p-4">
                            <!-- Loading State -->
                            <div id="loadingState" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3 text-muted">Validating reset token...</p>
                            </div>

                            <!-- Invalid Token State -->
                            <div id="invalidTokenState" class="text-center" style="display: none;">
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                                    <h5>Invalid or Expired Reset Link</h5>
                                    <p class="mb-3">This password reset link is either invalid, expired, or has already been used.</p>
                                    <a href="patient-login.html" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Login
                                    </a>
                                </div>
                            </div>

                            <!-- Reset Form State -->
                            <div id="resetFormState" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Patient:</strong> <span id="patientName">Loading...</span><br>
                                    <strong>Email:</strong> <span id="patientEmail">Loading...</span>
                                </div>

                                <form id="newPasswordForm" novalidate>
                                    <div class="mb-3">
                                        <label for="newPassword" class="form-label">New Password *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="newPassword" name="newPassword" required 
                                                   placeholder="Enter new password" minlength="6">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('newPassword', 'toggleNewPassword')">
                                                <i class="fas fa-eye" id="toggleNewPassword"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">Password must be at least 6 characters long.</div>
                                        <div class="form-text">Password must be at least 6 characters long.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">Confirm New Password *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required 
                                                   placeholder="Confirm new password" minlength="6">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('confirmPassword', 'toggleConfirmPassword')">
                                                <i class="fas fa-eye" id="toggleConfirmPassword"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">Passwords must match.</div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>Update Password
                                        </button>
                                        <a href="patient-login.html" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Login
                                        </a>
                                    </div>
                                </form>
                            </div>

                            <!-- Success State -->
                            <div id="successState" class="text-center" style="display: none;">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                                    <h5>Password Updated Successfully!</h5>
                                    <p class="mb-3">Your password has been updated. You can now login with your new password.</p>
                                    <a href="patient-login.html" class="btn btn-success">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Tips -->
                    <div class="card mt-4 border-0 bg-transparent">
                        <div class="card-body">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-shield-alt me-2"></i>Password Security Tips
                            </h6>
                            <ul class="list-unstyled text-muted small">
                                <li><i class="fas fa-check text-success me-2"></i>Use at least 8 characters</li>
                                <li><i class="fas fa-check text-success me-2"></i>Include uppercase and lowercase letters</li>
                                <li><i class="fas fa-check text-success me-2"></i>Add numbers and special characters</li>
                                <li><i class="fas fa-check text-success me-2"></i>Avoid common words or personal information</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Alert Container -->
    <div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentToken = null;
        let tokenData = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeResetPage();
        });

        function initializeResetPage() {
            // Get token from URL
            const urlParams = new URLSearchParams(window.location.search);
            currentToken = urlParams.get('token');

            if (!currentToken) {
                showInvalidToken();
                return;
            }

            // Validate token
            validateResetToken(currentToken);

            // Setup form submission
            document.getElementById('newPasswordForm').addEventListener('submit', handlePasswordReset);
        }

        function validateResetToken(token) {
            setTimeout(() => {
                const resetTokens = JSON.parse(localStorage.getItem('passwordResetTokens') || '[]');
                const tokenRecord = resetTokens.find(t => t.token === token && !t.used);

                if (!tokenRecord) {
                    showInvalidToken();
                    return;
                }

                // Check if token is expired
                const now = new Date();
                const expiry = new Date(tokenRecord.expiry);

                if (now > expiry) {
                    showInvalidToken();
                    return;
                }

                // Token is valid
                tokenData = tokenRecord;
                showResetForm();
            }, 1500); // Simulate loading time
        }

        function showInvalidToken() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('invalidTokenState').style.display = 'block';
        }

        function showResetForm() {
            // Get patient data
            const patientCredentials = loadPatientCredentials();
            const patient = patientCredentials[tokenData.patientId];

            if (patient) {
                document.getElementById('patientName').textContent = patient.name;
                document.getElementById('patientEmail').textContent = tokenData.email;
            }

            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('resetFormState').style.display = 'block';
        }

        function showSuccess() {
            document.getElementById('resetFormState').style.display = 'none';
            document.getElementById('successState').style.display = 'block';
        }

        function handlePasswordReset(e) {
            e.preventDefault();

            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validate passwords
            if (newPassword.length < 6) {
                showAlert('Password must be at least 6 characters long.', 'danger');
                return;
            }

            if (newPassword !== confirmPassword) {
                showAlert('Passwords do not match.', 'danger');
                return;
            }

            // Update password
            updatePatientPassword(tokenData.patientId, newPassword);

            // Mark token as used
            markTokenAsUsed(currentToken);

            // Show success
            showSuccess();
        }

        function updatePatientPassword(patientId, newPassword) {
            // Update in patient credentials
            const patientCredentials = loadPatientCredentials();
            if (patientCredentials[patientId]) {
                patientCredentials[patientId].password = newPassword;
                localStorage.setItem('patientCredentials', JSON.stringify(patientCredentials));
            }

            // Update in registered patients if exists
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients') || '[]');
            const patientIndex = registeredPatients.findIndex(p => p.patientId === patientId);
            if (patientIndex !== -1) {
                registeredPatients[patientIndex].password = newPassword;
                localStorage.setItem('registeredPatients', JSON.stringify(registeredPatients));
            }
        }

        function markTokenAsUsed(token) {
            const resetTokens = JSON.parse(localStorage.getItem('passwordResetTokens') || '[]');
            const tokenIndex = resetTokens.findIndex(t => t.token === token);
            if (tokenIndex !== -1) {
                resetTokens[tokenIndex].used = true;
                resetTokens[tokenIndex].usedAt = new Date().toISOString();
                localStorage.setItem('passwordResetTokens', JSON.stringify(resetTokens));
            }
        }

        function loadPatientCredentials() {
            return JSON.parse(localStorage.getItem('patientCredentials') || '{}');
        }

        function togglePasswordVisibility(inputId, buttonId) {
            const input = document.getElementById(inputId);
            const button = document.getElementById(buttonId);
            
            if (input.type === 'password') {
                input.type = 'text';
                button.classList.remove('fa-eye');
                button.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                button.classList.remove('fa-eye-slash');
                button.classList.add('fa-eye');
            }
        }

        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Make functions global
        window.togglePasswordVisibility = togglePasswordVisibility;
    </script>
</body>
</html>
