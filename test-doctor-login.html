<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Doctor Login - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-user-md me-2"></i>
                            Doctor Login Test
                        </h2>
                        <p class="mb-0 mt-2">Test the doctor login functionality</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Doctor Login Test Instructions</h5>
                            <p class="mb-3">Use these credentials to test the doctor login system:</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Primary Test Account:</h6>
                                    <ul>
                                        <li><strong>Doctor ID:</strong> DOC001</li>
                                        <li><strong>Password:</strong> doctor123</li>
                                        <li><strong>Name:</strong> Dr. Ahmed Musa</li>
                                        <li><strong>Specialty:</strong> General Medicine</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Other Available Accounts:</h6>
                                    <ul>
                                        <li><strong>DOC002</strong> - Dr. Fatima Ibrahim (Pediatrics)</li>
                                        <li><strong>DOC003</strong> - Dr. John Adamu (Cardiology)</li>
                                        <li><strong>DOC004</strong> - Dr. Aisha Bello (Dermatology)</li>
                                        <li><strong>DOC005</strong> - Dr. Mohammed Sani (Orthopedics)</li>
                                    </ul>
                                    <small class="text-muted">All use password: <strong>doctor123</strong></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-play me-2"></i>Quick Test</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Test the login system directly:</p>
                                        <div class="d-grid">
                                            <a href="doctor-login.html" class="btn btn-success">
                                                <i class="fas fa-sign-in-alt me-2"></i>Go to Doctor Login
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Test Functions</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Test login functions programmatically:</p>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-info btn-sm" onclick="testValidation()">
                                                Test Validation
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" onclick="testCredentials()">
                                                Test Credentials
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" onclick="showDoctorList()">
                                                Show All Doctors
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                            <div id="testResults" class="border rounded p-3 bg-light">
                                <p class="text-muted mb-0">Click the test buttons above to see results...</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-list-check me-2"></i>Test Checklist</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test1">
                                            <label for="test1">Doctor login page loads correctly</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test2">
                                            <label for="test2">Sample credentials are displayed</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test3">
                                            <label for="test3">Login with DOC001/doctor123 works</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test4">
                                            <label for="test4">Invalid credentials show error</label>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test5">
                                            <label for="test5">Success message appears on login</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test6">
                                            <label for="test6">Redirects to doctor dashboard</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test7">
                                            <label for="test7">Session data is stored correctly</label>
                                        </li>
                                        <li class="list-group-item">
                                            <input type="checkbox" class="form-check-input me-2" id="test8">
                                            <label for="test8">All 5 doctor accounts work</label>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-outline-primary me-3">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                            <a href="test-all-features.html" class="btn btn-outline-info">
                                <i class="fas fa-cogs me-2"></i>All System Tests
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/doctor-login.js"></script>
    <script>
        function testValidation() {
            const results = document.getElementById('testResults');
            
            // Test the validation function
            const testCases = [
                { id: 'DOC001', password: 'doctor123', expected: true },
                { id: 'DOC002', password: 'doctor123', expected: true },
                { id: 'DOC001', password: 'wrong', expected: false },
                { id: 'INVALID', password: 'doctor123', expected: false },
                { id: '', password: 'doctor123', expected: false }
            ];
            
            let html = '<h6>Validation Test Results:</h6><ul>';
            
            testCases.forEach(test => {
                const result = validateDoctorCredentials(test.id, test.password);
                const status = result === test.expected ? '✅' : '❌';
                html += `<li>${status} ${test.id}/${test.password} - Expected: ${test.expected}, Got: ${result}</li>`;
            });
            
            html += '</ul>';
            results.innerHTML = html;
        }
        
        function testCredentials() {
            const results = document.getElementById('testResults');
            const doctors = getAllDoctors();
            
            let html = '<h6>Available Doctor Credentials:</h6><div class="table-responsive"><table class="table table-sm"><thead><tr><th>ID</th><th>Name</th><th>Specialty</th><th>Status</th></tr></thead><tbody>';
            
            Object.keys(doctors).forEach(id => {
                const doctor = doctors[id];
                html += `<tr><td>${id}</td><td>${doctor.name}</td><td>${doctor.specialty}</td><td><span class="badge bg-success">${doctor.status}</span></td></tr>`;
            });
            
            html += '</tbody></table></div>';
            results.innerHTML = html;
        }
        
        function showDoctorList() {
            const results = document.getElementById('testResults');
            const doctors = getAllDoctors();
            
            let html = '<h6>Complete Doctor Information:</h6>';
            
            Object.keys(doctors).forEach(id => {
                const doctor = doctors[id];
                html += `
                    <div class="card mb-2">
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>${doctor.name}</strong> (${id})<br>
                                    <small>Specialty: ${doctor.specialty}</small>
                                </div>
                                <div class="col-md-6">
                                    <small>Email: ${doctor.email}<br>
                                    Phone: ${doctor.phone}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            results.innerHTML = html;
        }
    </script>
</body>
</html>
