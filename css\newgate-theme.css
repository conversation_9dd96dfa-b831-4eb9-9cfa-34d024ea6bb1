/* Newgate Hospital Management System - Custom Theme */
/* Color Standards Implementation */

:root {
    /* Primary Color Palette */
    --primary-color: #007BFF;
    --secondary-color: #6C757D;
    --background-color: #F8F9FA;
    --surface-color: #FFFFFF;
    --success-color: #28A745;
    --warning-color: #FFC107;
    --danger-color: #DC3545;
    --info-color: #17A2B8;
    --text-primary: #343A40;
    --text-secondary: #6C757D;
    
    /* Gradient Variations */
    --primary-gradient: linear-gradient(135deg, #007BFF 0%, #0056b3 100%);
    --success-gradient: linear-gradient(135deg, #28A745 0%, #1e7e34 100%);
    --warning-gradient: linear-gradient(135deg, #FFC107 0%, #e0a800 100%);
    --danger-gradient: linear-gradient(135deg, #DC3545 0%, #c82333 100%);
    --info-gradient: linear-gradient(135deg, #17A2B8 0%, #138496 100%);
    
    /* Shadow and Border */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 123, 255, 0.175);
    --border-radius: 0.375rem;
    --border-color: rgba(0, 123, 255, 0.125);
}

/* Global Styles */
body {
    background-color: var(--background-color);
    color: var(--text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation Styles */
.navbar {
    background: var(--primary-gradient) !important;
    box-shadow: var(--shadow-md);
    border-bottom: 3px solid var(--primary-color);
}

.navbar-brand {
    color: var(--surface-color) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--surface-color) !important;
    transform: translateY(-1px);
}

/* Card Styles */
.card {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: var(--primary-gradient);
    color: var(--surface-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

.card-header.bg-success {
    background: var(--success-gradient) !important;
}

.card-header.bg-warning {
    background: var(--warning-gradient) !important;
    color: var(--text-primary) !important;
}

.card-header.bg-danger {
    background: var(--danger-gradient) !important;
}

.card-header.bg-info {
    background: var(--info-gradient) !important;
}

/* Button Styles */
.btn-primary {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: var(--surface-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    border-color: var(--success-color);
    color: var(--surface-color);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning-gradient);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--danger-gradient);
    border-color: var(--danger-color);
    color: var(--surface-color);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
}

.btn-info {
    background: var(--info-gradient);
    border-color: var(--info-color);
    color: var(--surface-color);
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
}

/* Dashboard Specific Styles */
.dashboard-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.sidebar {
    background: var(--surface-color);
    border-right: 3px solid var(--primary-color);
    box-shadow: var(--shadow-md);
}

.sidebar-nav a {
    color: var(--text-primary);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover {
    background: var(--primary-color);
    color: var(--surface-color);
    transform: translateX(5px);
}

.sidebar-nav a.active {
    background: var(--primary-gradient);
    color: var(--surface-color);
    box-shadow: var(--shadow-md);
}

/* Form Styles */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert Styles */
.alert-primary {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

.bg-primary {
    background: var(--primary-gradient) !important;
}

.bg-success {
    background: var(--success-gradient) !important;
}

.bg-warning {
    background: var(--warning-gradient) !important;
}

.bg-danger {
    background: var(--danger-gradient) !important;
}

.bg-info {
    background: var(--info-gradient) !important;
}

/* Table Styles */
.table {
    background-color: var(--surface-color);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 123, 255, 0.05);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Modal Styles */
.modal-content {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: var(--primary-gradient);
    color: var(--surface-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Toast Styles */
.toast {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

/* Progress Bar Styles */
.progress {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: var(--border-radius);
}

.progress-bar {
    background: var(--primary-gradient);
}

/* Text Colors */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: #856404 !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* Interactive Elements */
.interactive-hover {
    transition: all 0.3s ease;
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Footer Styles */
.footer {
    background: var(--text-primary);
    color: var(--surface-color);
}

/* Loading Spinner */
.spinner-border-primary {
    color: var(--primary-color);
}



/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .sidebar, .btn {
        display: none !important;
    }

    .card {
        border: 1px solid var(--text-primary) !important;
        box-shadow: none !important;
    }
}

/* Hero Section Enhancements */
.bg-gradient-primary {
    background: linear-gradient(rgba(0, 123, 255, 0.8), rgba(0, 86, 179, 0.8)), url('../IB.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.hero-badge {
    /* Animation removed */
}

.hero-actions .btn {
    transition: all 0.3s ease;
    border: none;
    font-weight: 600;
}

.hero-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.hero-stats .stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Service Cards */
.service-card-featured {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.service-card-featured:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.service-card-featured .service-icon {
    transition: all 0.3s ease;
}

.service-card-featured:hover .service-icon {
    transform: scale(1.1);
}

/* Animations removed for better performance */
