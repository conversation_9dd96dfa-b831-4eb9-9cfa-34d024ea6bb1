<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Personalization Test - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0"><i class="fas fa-check-double me-2"></i>Complete Patient Personalization Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-star me-2"></i>Full System Test</h5>
                            <p class="mb-2">This comprehensive test verifies that ALL sections show personalized patient data:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>Dashboard Overview</strong> - Personal welcome & stats</li>
                                        <li>✅ <strong>Medical Records</strong> - Your registration details</li>
                                        <li>✅ <strong>Appointments</strong> - Your scheduled appointments</li>
                                        <li>✅ <strong>Prescriptions</strong> - Your medications</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>Lab Results</strong> - Your test results</li>
                                        <li>✅ <strong>Billing</strong> - Your charges & payments</li>
                                        <li>✅ <strong>Insurance</strong> - Your coverage info</li>
                                        <li>✅ <strong>Activity Timeline</strong> - Your actions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <h5><i class="fas fa-user-plus me-2 text-primary"></i>Step 1: Register</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>Complete Test Profile:</h6>
                                        <small>
                                            <strong>Name:</strong> Alex TestComplete<br>
                                            <strong>Email:</strong> <EMAIL><br>
                                            <strong>Phone:</strong> +234-999-COMPLETE<br>
                                            <strong>DOB:</strong> 1990-12-25<br>
                                            <strong>Gender:</strong> Female<br>
                                            <strong>Blood:</strong> A+<br>
                                            <strong>City:</strong> CompleteTest City<br>
                                            <strong>Emergency:</strong> Test Emergency<br>
                                            <strong>Insurance:</strong> Test Insurance Co.<br>
                                            <strong>Password:</strong> complete123
                                        </small>
                                    </div>
                                </div>
                                <a href="patient-registration.html" class="btn btn-primary w-100 mb-3">
                                    <i class="fas fa-user-plus me-2"></i>Register Complete Profile
                                </a>
                            </div>
                            
                            <div class="col-md-4">
                                <h5><i class="fas fa-sign-in-alt me-2 text-success"></i>Step 2: Login & Test</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>After Registration:</h6>
                                        <small>
                                            1. Save your <strong>Patient ID</strong><br>
                                            2. Login with credentials<br>
                                            3. Test ALL dashboard sections<br>
                                            4. Verify personalized data<br>
                                            5. Check activity timeline<br>
                                            6. Test quick actions
                                        </small>
                                    </div>
                                </div>
                                <a href="patient-login.html" class="btn btn-success w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login & Test All
                                </a>
                            </div>
                            
                            <div class="col-md-4">
                                <h5><i class="fas fa-clipboard-check me-2 text-info"></i>Step 3: Verify</h5>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6>Check These Sections:</h6>
                                        <small>
                                            ✓ Dashboard shows "Alex TestComplete"<br>
                                            ✓ Medical records show your details<br>
                                            ✓ Appointments section personalized<br>
                                            ✓ Prescriptions show welcome meds<br>
                                            ✓ Lab results show basic panel<br>
                                            ✓ Billing shows registration fee<br>
                                            ✓ Insurance shows your provider<br>
                                            ✓ Activity shows your timeline
                                        </small>
                                    </div>
                                </div>
                                <a href="patient-dashboard.html" class="btn btn-info w-100 mb-3">
                                    <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                                </a>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-list-check me-2 text-success"></i>Expected Personalized Data</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Section</th>
                                                <th>Should Show</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>Welcome</strong></td>
                                                <td>"Welcome, Alex TestComplete"</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Email</strong></td>
                                                <td><EMAIL></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Phone</strong></td>
                                                <td>+234-999-COMPLETE</td>
                                            </tr>
                                            <tr>
                                                <td><strong>DOB</strong></td>
                                                <td>December 25, 1990</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Gender</strong></td>
                                                <td>Female</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Blood</strong></td>
                                                <td>A+</td>
                                            </tr>
                                            <tr>
                                                <td><strong>City</strong></td>
                                                <td>CompleteTest City</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Emergency</strong></td>
                                                <td>Test Emergency</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Insurance</strong></td>
                                                <td>Test Insurance Co.</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5><i class="fas fa-tools me-2 text-warning"></i>Debug & Test Tools</h5>
                                
                                <div class="row">
                                    <div class="col-6">
                                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="showCurrentUser()">
                                            <i class="fas fa-user me-1"></i>Current User
                                        </button>
                                        <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="showMedicalData()">
                                            <i class="fas fa-file-medical me-1"></i>Medical Data
                                        </button>
                                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="showBillingData()">
                                            <i class="fas fa-file-invoice me-1"></i>Billing Data
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="testAllSections()">
                                            <i class="fas fa-check-double me-1"></i>Test All
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="clearAllData()">
                                            <i class="fas fa-trash me-1"></i>Clear All
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="exportAllData()">
                                            <i class="fas fa-download me-1"></i>Export All
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="debugOutput" class="mt-3" style="max-height: 300px; overflow-y: auto;"></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>Success Criteria:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>Dashboard shows your actual name everywhere</li>
                                        <li>Medical records display your registration data</li>
                                        <li>All contact info matches your input</li>
                                        <li>Demographics are correctly formatted</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>Activity timeline shows your actions</li>
                                        <li>Billing shows your account status</li>
                                        <li>Insurance reflects your provider</li>
                                        <li>No hardcoded sample data visible</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showCurrentUser() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            const output = document.getElementById('debugOutput');
            
            if (!userData) {
                output.innerHTML = '<div class="alert alert-warning">No user currently logged in.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            output.innerHTML = `
                <div class="alert alert-info">
                    <h6>Current User Data:</h6>
                    <pre style="font-size: 12px;">${JSON.stringify(user, null, 2)}</pre>
                </div>
            `;
        }
        
        function showMedicalData() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            if (!userData) {
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-warning">No user logged in.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            const medicalData = localStorage.getItem(`medicalData_${user.id}`);
            const output = document.getElementById('debugOutput');
            
            if (!medicalData) {
                output.innerHTML = '<div class="alert alert-warning">No medical data found.</div>';
                return;
            }
            
            output.innerHTML = `
                <div class="alert alert-success">
                    <h6>Medical Data for ${user.name}:</h6>
                    <pre style="font-size: 12px;">${JSON.stringify(JSON.parse(medicalData), null, 2)}</pre>
                </div>
            `;
        }
        
        function showBillingData() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            if (!userData) {
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-warning">No user logged in.</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            const billingData = localStorage.getItem(`billingData_${user.id}`);
            const output = document.getElementById('debugOutput');
            
            if (!billingData) {
                output.innerHTML = '<div class="alert alert-warning">No billing data found.</div>';
                return;
            }
            
            output.innerHTML = `
                <div class="alert alert-primary">
                    <h6>Billing Data for ${user.name}:</h6>
                    <pre style="font-size: 12px;">${JSON.stringify(JSON.parse(billingData), null, 2)}</pre>
                </div>
            `;
        }
        
        function testAllSections() {
            const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
            const output = document.getElementById('debugOutput');
            
            if (!userData) {
                output.innerHTML = '<div class="alert alert-danger">❌ No user logged in - Please login first!</div>';
                return;
            }
            
            const user = JSON.parse(userData);
            const medicalData = localStorage.getItem(`medicalData_${user.id}`);
            const billingData = localStorage.getItem(`billingData_${user.id}`);
            
            let results = '<div class="alert alert-info"><h6>Personalization Test Results:</h6>';
            
            // Test user data
            results += `<p>✅ <strong>User Login:</strong> ${user.name} (${user.id})</p>`;
            results += `<p>✅ <strong>Email:</strong> ${user.email}</p>`;
            results += `<p>✅ <strong>Phone:</strong> ${user.phone}</p>`;
            
            // Test medical data
            if (medicalData) {
                results += `<p>✅ <strong>Medical Data:</strong> Found and personalized</p>`;
            } else {
                results += `<p>❌ <strong>Medical Data:</strong> Missing</p>`;
            }
            
            // Test billing data
            if (billingData) {
                results += `<p>✅ <strong>Billing Data:</strong> Found and personalized</p>`;
            } else {
                results += `<p>❌ <strong>Billing Data:</strong> Missing</p>`;
            }
            
            results += '<p><strong>✅ All sections should now show your personal data!</strong></p>';
            results += '</div>';
            
            output.innerHTML = results;
        }
        
        function clearAllData() {
            if (confirm('Clear ALL test data? This will remove all patients and reset the system.')) {
                // Clear all localStorage
                Object.keys(localStorage).forEach(key => {
                    if (key.includes('registeredPatients') || 
                        key.includes('medicalData_') || 
                        key.includes('billingData_') || 
                        key.includes('hospitalUser')) {
                        localStorage.removeItem(key);
                    }
                });
                
                sessionStorage.clear();
                document.getElementById('debugOutput').innerHTML = '<div class="alert alert-success">✅ All data cleared! Ready for fresh testing.</div>';
            }
        }
        
        function exportAllData() {
            const allData = {
                registeredPatients: JSON.parse(localStorage.getItem('registeredPatients') || '{}'),
                currentUser: JSON.parse(localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser') || '{}'),
                medicalData: {},
                billingData: {}
            };
            
            // Export all medical and billing data
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('medicalData_')) {
                    allData.medicalData[key] = JSON.parse(localStorage.getItem(key));
                }
                if (key.startsWith('billingData_')) {
                    allData.billingData[key] = JSON.parse(localStorage.getItem(key));
                }
            });
            
            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'complete-patient-data.json';
            link.click();
            
            document.getElementById('debugOutput').innerHTML = '<div class="alert alert-success">✅ All data exported successfully!</div>';
        }
    </script>
</body>
</html>
