<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nurse Login - Newgate Hospital System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.html">
                <i class="fas fa-hospital-alt me-2"></i>
                Newgate Hospital System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index-redesigned.html">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="loginDropdown" role="button" data-bs-toggle="dropdown">
                            Other Logins
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="patient-login.html"><i class="fas fa-user me-2"></i>Patient Login</a></li>
                            <li><a class="dropdown-item" href="doctor-login.html"><i class="fas fa-user-md me-2"></i>Doctor Login</a></li>
                            <li><a class="dropdown-item" href="admin-login.html"><i class="fas fa-user-shield me-2"></i>Admin Login</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Login Section -->
    <section class="py-5 mt-5">
        <div class="container">
            <div class="row justify-content-center align-items-center min-vh-100">
                <div class="col-lg-5 col-md-7">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-info text-white text-center py-4">
                            <h2 class="mb-0">
                                <i class="fas fa-user-nurse me-2"></i>
                                Nurse Login
                            </h2>
                            <p class="mb-0 mt-2">Access your nursing portal</p>
                        </div>
                        <div class="card-body p-5">
                            <form id="nurseLoginForm" novalidate>
                                <div class="mb-4">
                                    <label for="nurseId" class="form-label">Nurse ID *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-id-badge text-info"></i>
                                        </span>
                                        <input type="text" class="form-control" id="nurseId" name="nurseId" required placeholder="Enter your Nurse ID">
                                    </div>
                                    <div class="invalid-feedback">Please provide your Nurse ID.</div>
                                </div>

                                <div class="mb-4">
                                    <label for="loginPassword" class="form-label">Password *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock text-info"></i>
                                        </span>
                                        <input type="password" class="form-control" id="loginPassword" name="loginPassword" required placeholder="Enter your password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">Please provide your password.</div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                                        <label class="form-check-label" for="rememberMe">
                                            Remember me
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mb-4">
                                    <button type="submit" class="btn btn-info btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </button>
                                </div>

                                <div class="text-center">
                                    <a href="#" class="text-info text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                        <i class="fas fa-key me-1"></i>Forgot Password?
                                    </a>
                                </div>
                            </form>

                            <!-- Error/Success Messages -->
                            <div id="errorMessage" class="mt-3"></div>

                            <hr class="my-4">

                            <!-- Sample Credentials -->
                            <div id="sampleCredentials"></div>

                            <div class="text-center">
                                <p class="text-muted mb-3">Need help accessing your account?</p>
                                <a href="#" class="btn btn-outline-info" onclick="contactIT()">
                                    <i class="fas fa-headset me-2"></i>Contact IT Support
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access Links -->
                    <div class="row mt-4">
                        <div class="col-6">
                            <div class="card text-center border-0 bg-light">
                                <div class="card-body py-3">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-heartbeat text-info fa-2x mb-2"></i>
                                        <p class="mb-0 text-dark">Patient Care</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card text-center border-0 bg-light">
                                <div class="card-body py-3">
                                    <a href="#" class="text-decoration-none">
                                        <i class="fas fa-ambulance text-danger fa-2x mb-2"></i>
                                        <p class="mb-0 text-dark">Emergency</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="forgotPasswordModalLabel">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="forgotPasswordForm" novalidate>
                        <div class="mb-3">
                            <label for="resetNurseId" class="form-label">Nurse ID *</label>
                            <input type="text" class="form-control" id="resetNurseId" name="resetNurseId" required placeholder="Enter your Nurse ID">
                            <div class="invalid-feedback">Please provide your Nurse ID.</div>
                            <div class="form-text">We'll send a reset link to your registered email.</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-info" id="sendResetLink">
                        <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Newgate Hospital Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Final Year Project - Newgate University Minna</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/nurse-login.js"></script>
</body>
</html>
