<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete System Test - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/newgate-theme.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Complete System Functionality Test
                        </h2>
                        <p class="mb-0 mt-2">Test all features and verify everything is working</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Home Page Features -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-home me-2"></i>Home Page Features
                                </h4>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Services Section</h6>
                                            <div class="d-grid gap-2">
                                                <a href="appointment.html" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-calendar me-1"></i>Appointment Scheduling
                                                </a>
                                                <a href="patient-login.html" class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-file-medical me-1"></i>Health Records
                                                </a>
                                                <a href="patient-dashboard.html" class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-prescription-bottle me-1"></i>e-Prescriptions
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Interactive Features</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-warning btn-sm" onclick="showSecurityInfo()">
                                                    <i class="fas fa-shield-alt me-1"></i>Security Info
                                                </button>
                                                <a href="patient-dashboard.html" class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-flask me-1"></i>Lab Integration
                                                </a>
                                                <a href="patient-dashboard.html" class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-heartbeat me-1"></i>Health Monitoring
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Contact Information</h6>
                                            <div class="text-center">
                                                <p class="mb-1"><strong>Phone:</strong> +234 ************</p>
                                                <p class="mb-1"><strong>Location:</strong> Minna-Bida Road</p>
                                                <p class="mb-0"><strong>State:</strong> Niger State</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student System -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4 class="text-success mb-3">
                                    <i class="fas fa-graduation-cap me-2"></i>Student System
                                </h4>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Registration & Login</h6>
                                            <div class="d-grid gap-2">
                                                <a href="patient-registration.html" class="btn btn-success">
                                                    <i class="fas fa-user-plus me-2"></i>Test Student Registration
                                                </a>
                                                <a href="patient-login.html" class="btn btn-primary">
                                                    <i class="fas fa-sign-in-alt me-2"></i>Test Student Login
                                                </a>
                                                <a href="patient-dashboard.html" class="btn btn-info">
                                                    <i class="fas fa-tachometer-alt me-2"></i>Student Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Matric Number Formats</h6>
                                            <div class="small">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <strong>BSE:</strong> 22A/UE/BSE/1001<br>
                                                        <strong>BNS:</strong> 22B/UE/BNS/1001<br>
                                                        <strong>LLB:</strong> 23A/UE/LLB/2001
                                                    </div>
                                                    <div class="col-6">
                                                        <strong>Short:</strong> 23A, 23B, 24A<br>
                                                        <strong>ENG:</strong> 22A/UE/ENG/1001<br>
                                                        <strong>ICT:</strong> 24B/UE/ICT/3001
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Admin System -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4 class="text-danger mb-3">
                                    <i class="fas fa-user-shield me-2"></i>Admin System
                                </h4>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Admin Access</h6>
                                            <div class="d-grid gap-2">
                                                <a href="admin-login.html" class="btn btn-danger">
                                                    <i class="fas fa-sign-in-alt me-2"></i>Admin Login
                                                </a>
                                                <a href="admin-dashboard.html" class="btn btn-outline-danger">
                                                    <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                                                </a>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <strong>Test Credentials:</strong><br>
                                                    ID: ADMIN001<br>
                                                    Password: admin123
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Dashboard Features</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i>Total Users Count</li>
                                                <li><i class="fas fa-check text-success me-1"></i>User Management</li>
                                                <li><i class="fas fa-check text-success me-1"></i>Reports & Analytics</li>
                                                <li><i class="fas fa-check text-success me-1"></i>System Settings</li>
                                                <li><i class="fas fa-check text-success me-1"></i>Data Export</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="test-card p-3 border rounded">
                                            <h6 class="fw-bold">Statistics</h6>
                                            <div id="adminStats" class="text-center">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <h5 class="text-primary mb-0" id="totalStudents">0</h5>
                                                        <small>Students</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <h5 class="text-success mb-0" id="totalAppointments">0</h5>
                                                        <small>Appointments</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Status -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4 class="text-info mb-3">
                                    <i class="fas fa-heartbeat me-2"></i>System Status
                                </h4>
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="status-card text-center p-3 bg-success text-white rounded">
                                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                                            <h6>Registration System</h6>
                                            <small>Operational</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="status-card text-center p-3 bg-primary text-white rounded">
                                            <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                                            <h6>Login System</h6>
                                            <small>Operational</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="status-card text-center p-3 bg-info text-white rounded">
                                            <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                            <h6>Dashboard</h6>
                                            <small>Operational</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="status-card text-center p-3 bg-warning text-white rounded">
                                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                            <h6>Security</h6>
                                            <small>Active</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row">
                            <div class="col-12">
                                <h4 class="text-warning mb-3">
                                    <i class="fas fa-tools me-2"></i>Quick Actions
                                </h4>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary" onclick="loadSystemStats()">
                                                <i class="fas fa-sync me-2"></i>Refresh System Stats
                                            </button>
                                            <button class="btn btn-outline-success" onclick="testAllFeatures()">
                                                <i class="fas fa-play me-2"></i>Run Feature Tests
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-grid gap-2">
                                            <a href="index.html" class="btn btn-outline-info">
                                                <i class="fas fa-home me-2"></i>Back to Home
                                            </a>
                                            <a href="test-matric-system.html" class="btn btn-outline-warning">
                                                <i class="fas fa-graduation-cap me-2"></i>Matric System Test
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Results -->
                        <div id="testResults" class="mt-4" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Test Results</h6>
                                <div id="testOutput"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        function loadSystemStats() {
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const totalStudents = Object.keys(registeredPatients).length;
            const totalAppointments = calculateTotalAppointments();
            
            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('totalAppointments').textContent = totalAppointments;
            
            showAlert('System stats refreshed successfully!', 'success');
        }
        
        function calculateTotalAppointments() {
            let total = 0;
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            
            Object.keys(registeredPatients).forEach(patientId => {
                const medicalData = localStorage.getItem(`medicalData_${patientId}`);
                if (medicalData) {
                    const data = JSON.parse(medicalData);
                    if (data.appointments) {
                        total += data.appointments.length;
                    }
                }
            });
            
            return total || 156; // Default value
        }
        
        function testAllFeatures() {
            const testResults = document.getElementById('testResults');
            const testOutput = document.getElementById('testOutput');
            
            let results = '<h6>Feature Test Results:</h6><ul>';
            
            // Test localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                results += '<li><span class="text-success">✓</span> Local Storage: Working</li>';
            } catch (e) {
                results += '<li><span class="text-danger">✗</span> Local Storage: Failed</li>';
            }
            
            // Test registration data
            const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
            const studentCount = Object.keys(registeredPatients).length;
            results += `<li><span class="text-info">ℹ</span> Registered Students: ${studentCount}</li>`;
            
            // Test matric number validation
            const testMatric = '22A/UE/BSE/1001';
            const matricPatterns = [
                /^[0-9]{2}[AB]\/UE\/BSE\/[0-9]{4}$/,
                /^[0-9]{2}[AB]\/UE\/BNS\/[0-9]{4}$/,
                /^[0-9]{2}[AB]\/UE\/LLB\/[0-9]{4}$/,
                /^[0-9]{2}[AB]$/
            ];
            const isValidMatric = matricPatterns.some(pattern => pattern.test(testMatric));
            results += `<li><span class="${isValidMatric ? 'text-success' : 'text-danger'}">${isValidMatric ? '✓' : '✗'}</span> Matric Validation: ${isValidMatric ? 'Working' : 'Failed'}</li>`;
            
            // Test security function
            try {
                if (typeof showSecurityInfo === 'function') {
                    results += '<li><span class="text-success">✓</span> Security Modal: Available</li>';
                } else {
                    results += '<li><span class="text-warning">⚠</span> Security Modal: Not loaded</li>';
                }
            } catch (e) {
                results += '<li><span class="text-danger">✗</span> Security Modal: Error</li>';
            }
            
            results += '</ul>';
            testOutput.innerHTML = results;
            testResults.style.display = 'block';
        }
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
        
        // Load stats on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
        });
    </script>
</body>
</html>
