// Admin Dashboard JavaScript

let currentAdmin = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeAdminDashboard();
});

function initializeAdminDashboard() {
    // Check authentication
    if (!checkAdminAuthentication()) {
        return;
    }
    
    // Load admin data
    loadAdminData();
    
    // Load dashboard statistics
    loadDashboardStats();
    
    // Initialize sidebar navigation
    initializeSidebarNavigation();
    
    // Load recent activities
    loadRecentActivities();
}

function checkAdminAuthentication() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (!userData) {
        redirectToLogin();
        return false;
    }
    
    const user = JSON.parse(userData);
    if (user.role !== 'admin') {
        showErrorAlert('Access denied. Admin privileges required.');
        redirectToLogin();
        return false;
    }
    
    currentAdmin = user;
    return true;
}

function redirectToLogin() {
    setTimeout(() => {
        window.location.href = 'admin-login.html';
    }, 2000);
}

function loadAdminData() {
    if (currentAdmin) {
        document.getElementById('adminName').textContent = currentAdmin.name;
        document.getElementById('welcomeName').textContent = currentAdmin.name;
        document.getElementById('adminId').textContent = currentAdmin.id;
        
        // Format last login time
        const loginTime = new Date(currentAdmin.loginTime);
        document.getElementById('lastLogin').textContent = formatDate(loginTime);
    }
}

function loadDashboardStats() {
    // Load real statistics from localStorage and system data
    const stats = getSystemStats();

    // Animate counters with real data
    animateCounter('totalUsers', stats.totalUsers);
    animateCounter('totalDoctors', stats.totalDoctors);
    animateCounter('totalNurses', stats.totalNurses);
    animateCounter('totalPatients', stats.totalPatients);
    animateCounter('totalAppointments', stats.totalAppointments);

    console.log('Dashboard stats loaded:', stats);
}

function getSystemStats() {
    // Get real data from localStorage
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const systemUsers = JSON.parse(localStorage.getItem('systemUsers')) || {};
    const appointments = JSON.parse(localStorage.getItem('systemAppointments')) || {};

    // Calculate real statistics
    const totalPatients = Object.keys(registeredPatients).length;
    const totalDoctors = Object.values(systemUsers).filter(user => user.role === 'doctor').length || 25; // Default if no doctors
    const totalNurses = Object.values(systemUsers).filter(user => user.role === 'nurse').length || 45; // Default if no nurses
    const totalUsers = totalPatients + totalDoctors + totalNurses;
    const totalAppointments = Object.keys(appointments).length || calculateTotalAppointments();

    return {
        totalUsers,
        totalDoctors,
        totalNurses,
        totalPatients,
        totalAppointments
    };
}

function calculateTotalAppointments() {
    // Calculate appointments from patient medical data
    let totalAppointments = 0;
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};

    Object.keys(registeredPatients).forEach(patientId => {
        const medicalData = localStorage.getItem(`medicalData_${patientId}`);
        if (medicalData) {
            const data = JSON.parse(medicalData);
            if (data.appointments) {
                totalAppointments += data.appointments.length;
            }
        }
    });

    return totalAppointments || 156; // Default value if no appointments found
}

function animateCounter(elementId, targetValue) {
    const element = document.getElementById(elementId);
    let currentValue = 0;
    const increment = targetValue / 50;
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }
        element.textContent = Math.floor(currentValue);
    }, 30);
}

function initializeSidebarNavigation() {
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            sidebarLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
        });
    });
}

function loadRecentActivities() {
    // This would typically load from an API
    const activities = [
        {
            time: '10:30 AM',
            user: 'Dr. Smith',
            action: 'Patient consultation completed',
            status: 'success'
        },
        {
            time: '10:15 AM',
            user: 'Nurse Johnson',
            action: 'Vital signs recorded',
            status: 'success'
        },
        {
            time: '09:45 AM',
            user: 'Patient John Doe',
            action: 'Appointment scheduled',
            status: 'pending'
        }
    ];
    
    const tbody = document.getElementById('recentActivities');
    tbody.innerHTML = activities.map(activity => `
        <tr>
            <td>${activity.time}</td>
            <td>${activity.user}</td>
            <td>${activity.action}</td>
            <td><span class="badge bg-${activity.status === 'success' ? 'success' : activity.status === 'pending' ? 'info' : 'warning'}">${activity.status}</span></td>
        </tr>
    `).join('');
}

// Navigation functions
function showDashboard() {
    // Show dashboard content (already visible by default)
    showSuccessAlert('Dashboard loaded successfully');
}

function showUsers() {
    showSuccessAlert('User Management section - Coming soon!');
}

function showDoctors() {
    showSuccessAlert('Doctor Management section - Coming soon!');
}

function showNurses() {
    showSuccessAlert('Nurse Management section - Coming soon!');
}

function showPatients() {
    showSuccessAlert('Patient Management section - Coming soon!');
}

function showAppointments() {
    showSuccessAlert('Appointment Management section - Coming soon!');
}

function showReports() {
    showSuccessAlert('Reports section - Coming soon!');
}

function showSettings() {
    showSuccessAlert('System Settings section - Coming soon!');
}

// Utility functions
function formatDate(dateString) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

function showSuccessAlert(message) {
    const alert = document.getElementById('successAlert');
    const messageSpan = document.getElementById('successMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showErrorAlert(message) {
    const alert = document.getElementById('errorAlert');
    const messageSpan = document.getElementById('errorMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function logout() {
    // Clear user data
    localStorage.removeItem('hospitalUser');
    sessionStorage.clear();
    
    // Show success message
    showSuccessAlert('Logged out successfully!');
    
    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showUsers = showUsers;
window.showDoctors = showDoctors;
window.showNurses = showNurses;
window.showPatients = showPatients;
window.showAppointments = showAppointments;
window.showReports = showReports;
window.showSettings = showSettings;
window.logout = logout;

// Section management
function showSection(sectionName) {
    // Hide all content sections
    const sections = ['dashboardContent', 'doctorsContent', 'nursesContent', 'patientsContent', 'appointmentsContent', 'userManagementContent', 'reportsContent', 'systemSettingsContent'];
    sections.forEach(section => {
        const element = document.getElementById(section);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Show the requested section
    const targetSection = document.getElementById(sectionName + 'Content');
    if (targetSection) {
        targetSection.style.display = 'block';
    }

    // Update sidebar active state
    updateSidebarActive(sectionName);
}

function updateSidebarActive(activeSection) {
    // Remove active class from all sidebar links
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => link.classList.remove('active'));

    // Add active class to current section
    const activeLinkMap = {
        'dashboard': 0,
        'userManagement': 1,
        'doctors': 2,
        'nurses': 3,
        'patients': 4,
        'appointments': 5,
        'reports': 6,
        'systemSettings': 7
    };

    const linkIndex = activeLinkMap[activeSection];
    if (linkIndex !== undefined && sidebarLinks[linkIndex]) {
        sidebarLinks[linkIndex].classList.add('active');
    }
}

// Admin Dashboard Functions
function showDashboard() {
    console.log('Loading Dashboard...');
    showSection('dashboard');
    loadDashboardStats();
}

function showUserManagement() {
    console.log('Loading User Management...');
    showSection('userManagement');
    loadUserManagementData();
}

function showDoctors() {
    console.log('Loading Doctors...');
    showSection('doctors');
    loadDoctorsData();
}

function showNurses() {
    console.log('Loading Nurses...');
    showSection('nurses');
    loadNursesData();
}

function showPatients() {
    console.log('Loading Students...');
    showSection('patients');
    loadPatientsData();
}

function showAppointments() {
    console.log('Loading Appointments...');
    showSection('appointments');
    loadAppointmentsData();
}

function showReports() {
    console.log('Loading Reports...');
    showSection('reports');
    loadReportsData();
}

function showSystemSettings() {
    console.log('Loading System Settings...');
    showSection('systemSettings');
    loadSystemSettingsData();
}

function loadDoctorsData() {
    const doctorsContainer = document.getElementById('doctorsContent');

    if (doctorsContainer) {
        const sampleDoctors = [
            { id: 'DOC001', name: 'Dr. Ahmed Musa', specialty: 'General Medicine', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'DOC002', name: 'Dr. Fatima Ibrahim', specialty: 'Pediatrics', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'DOC003', name: 'Dr. John Adamu', specialty: 'Cardiology', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'DOC004', name: 'Dr. Aisha Bello', specialty: 'Dermatology', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'DOC005', name: 'Dr. Mohammed Sani', specialty: 'Orthopedics', email: '<EMAIL>', phone: '+234 ************' }
        ];

        let html = `
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-user-md me-2"></i>Medical Doctors</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Doctor ID</th>
                                    <th>Name</th>
                                    <th>Specialty</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        sampleDoctors.forEach(doctor => {
            html += `
                <tr>
                    <td>${doctor.id}</td>
                    <td>${doctor.name}</td>
                    <td>${doctor.specialty}</td>
                    <td>${doctor.email}</td>
                    <td>${doctor.phone}</td>
                    <td><span class="badge bg-success">Active</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editDoctor('${doctor.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info me-1" onclick="viewDoctorSchedule('${doctor.id}')">
                            <i class="fas fa-calendar"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="manageDoctorAccess('${doctor.id}')">
                            <i class="fas fa-key"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-success" onclick="addNewDoctor()">
                            <i class="fas fa-plus me-2"></i>Add New Doctor
                        </button>
                        <button class="btn btn-outline-primary ms-2" onclick="exportDoctorList()">
                            <i class="fas fa-download me-2"></i>Export List
                        </button>
                    </div>
                </div>
            </div>
        `;

        doctorsContainer.innerHTML = html;
    }
}

function loadNursesData() {
    const nursesContainer = document.getElementById('nursesContent');

    if (nursesContainer) {
        const sampleNurses = [
            { id: 'NUR001', name: 'Nurse Halima Usman', department: 'Emergency', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'NUR002', name: 'Nurse Grace Okafor', department: 'Pediatrics', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'NUR003', name: 'Nurse Amina Garba', department: 'General Ward', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'NUR004', name: 'Nurse Mary Yakubu', department: 'ICU', email: '<EMAIL>', phone: '+234 ************' },
            { id: 'NUR005', name: 'Nurse Zainab Mohammed', department: 'Outpatient', email: '<EMAIL>', phone: '+234 ************' }
        ];

        let html = `
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-user-nurse me-2"></i>Nursing Staff</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nurse ID</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        sampleNurses.forEach(nurse => {
            html += `
                <tr>
                    <td>${nurse.id}</td>
                    <td>${nurse.name}</td>
                    <td>${nurse.department}</td>
                    <td>${nurse.email}</td>
                    <td>${nurse.phone}</td>
                    <td><span class="badge bg-success">Active</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editNurse('${nurse.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info me-1" onclick="viewNurseSchedule('${nurse.id}')">
                            <i class="fas fa-calendar"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="manageNurseAccess('${nurse.id}')">
                            <i class="fas fa-key"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-info" onclick="addNewNurse()">
                            <i class="fas fa-plus me-2"></i>Add New Nurse
                        </button>
                        <button class="btn btn-outline-primary ms-2" onclick="exportNurseList()">
                            <i class="fas fa-download me-2"></i>Export List
                        </button>
                    </div>
                </div>
            </div>
        `;

        nursesContainer.innerHTML = html;
    }
}

function loadPatientsData() {
    const patientsContainer = document.getElementById('patientsContent');
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};

    if (patientsContainer) {
        let html = `
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Registered Students</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" placeholder="Search students..." id="studentSearch" onkeyup="filterStudents()">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-success" onclick="exportStudentList()">
                                <i class="fas fa-download me-2"></i>Export Students
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped" id="studentsTable">
                            <thead>
                                <tr>
                                    <th>Matric Number</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                    <th>Blood Group</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Registration Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        if (Object.keys(registeredPatients).length === 0) {
            html += '<tr><td colspan="9" class="text-center">No students registered yet</td></tr>';
        } else {
            Object.keys(registeredPatients).forEach(matricNumber => {
                const student = registeredPatients[matricNumber];
                const regDate = new Date(student.registrationDate).toLocaleDateString();
                html += `
                    <tr>
                        <td><strong>${matricNumber}</strong></td>
                        <td>${student.name}</td>
                        <td>${student.department || 'N/A'}</td>
                        <td>${student.bloodGroup || 'N/A'}</td>
                        <td>${student.email}</td>
                        <td>${student.phone}</td>
                        <td>${regDate}</td>
                        <td><span class="badge bg-success">Active</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary me-1" onclick="viewStudentDetails('${matricNumber}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-info me-1" onclick="editStudent('${matricNumber}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-warning me-1" onclick="viewStudentMedical('${matricNumber}')">
                                <i class="fas fa-file-medical"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteStudent('${matricNumber}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        patientsContainer.innerHTML = html;
    }
}

function loadAppointmentsData() {
    const appointmentsContainer = document.getElementById('appointmentsContent');

    if (appointmentsContainer) {
        // Generate sample appointments based on registered students
        const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
        const studentMatrics = Object.keys(registeredPatients);
        const sampleAppointments = [];

        // Create sample appointments
        const doctors = ['Dr. Ahmed Musa', 'Dr. Fatima Ibrahim', 'Dr. John Adamu', 'Dr. Aisha Bello'];
        const appointmentTypes = ['General Checkup', 'Follow-up', 'Emergency', 'Consultation', 'Lab Results'];
        const statuses = ['Scheduled', 'Completed', 'Cancelled', 'In Progress'];

        for (let i = 0; i < Math.min(10, studentMatrics.length * 2); i++) {
            const randomStudent = studentMatrics[Math.floor(Math.random() * studentMatrics.length)];
            const randomDoctor = doctors[Math.floor(Math.random() * doctors.length)];
            const randomType = appointmentTypes[Math.floor(Math.random() * appointmentTypes.length)];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            const randomDate = new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000);

            if (randomStudent) {
                sampleAppointments.push({
                    id: `APT${String(i + 1).padStart(3, '0')}`,
                    studentMatric: randomStudent,
                    studentName: registeredPatients[randomStudent]?.name || 'Unknown',
                    doctor: randomDoctor,
                    type: randomType,
                    date: randomDate.toLocaleDateString(),
                    time: `${Math.floor(Math.random() * 12) + 8}:${Math.random() > 0.5 ? '00' : '30'}`,
                    status: randomStatus
                });
            }
        }

        let html = `
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Appointments Management</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="appointmentFilter" onchange="filterAppointments()">
                                <option value="">All Appointments</option>
                                <option value="Scheduled">Scheduled</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                                <option value="In Progress">In Progress</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="date" class="form-control" id="appointmentDate" onchange="filterAppointments()">
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-warning" onclick="addNewAppointment()">
                                <i class="fas fa-plus me-2"></i>New Appointment
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped" id="appointmentsTable">
                            <thead>
                                <tr>
                                    <th>Appointment ID</th>
                                    <th>Student</th>
                                    <th>Matric Number</th>
                                    <th>Doctor</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        if (sampleAppointments.length === 0) {
            html += '<tr><td colspan="9" class="text-center">No appointments found</td></tr>';
        } else {
            sampleAppointments.forEach(appointment => {
                const statusClass = {
                    'Scheduled': 'bg-primary',
                    'Completed': 'bg-success',
                    'Cancelled': 'bg-danger',
                    'In Progress': 'bg-warning'
                }[appointment.status] || 'bg-secondary';

                html += `
                    <tr>
                        <td><strong>${appointment.id}</strong></td>
                        <td>${appointment.studentName}</td>
                        <td>${appointment.studentMatric}</td>
                        <td>${appointment.doctor}</td>
                        <td>${appointment.type}</td>
                        <td>${appointment.date}</td>
                        <td>${appointment.time}</td>
                        <td><span class="badge ${statusClass}">${appointment.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary me-1" onclick="viewAppointment('${appointment.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-info me-1" onclick="editAppointment('${appointment.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-success me-1" onclick="completeAppointment('${appointment.id}')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="cancelAppointment('${appointment.id}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        appointmentsContainer.innerHTML = html;
    }
}

function loadUserManagementData() {
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const userTableBody = document.querySelector('#userManagementContent tbody');

    if (userTableBody) {
        let html = '';
        Object.keys(registeredPatients).forEach(matricNumber => {
            const student = registeredPatients[matricNumber];
            html += `
                <tr>
                    <td>${matricNumber}</td>
                    <td>${student.name}</td>
                    <td>${student.email}</td>
                    <td>${student.department || 'N/A'}</td>
                    <td><span class="badge bg-success">Active</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editUser('${matricNumber}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteUser('${matricNumber}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        if (html === '') {
            html = '<tr><td colspan="6" class="text-center">No users found</td></tr>';
        }

        userTableBody.innerHTML = html;
    }
}

function loadReportsData() {
    const stats = getSystemStats();
    const reportsContainer = document.getElementById('reportsContent');

    if (reportsContainer) {
        reportsContainer.innerHTML = `
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>System Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <h6>Total Students</h6>
                                    <h3 class="text-primary">${stats.totalPatients}</h3>
                                </div>
                                <div class="col-6">
                                    <h6>Total Appointments</h6>
                                    <h3 class="text-success">${stats.totalAppointments}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-download me-2"></i>Export Reports</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="exportStudentReport()">
                                <i class="fas fa-users me-2"></i>Export Student Report
                            </button>
                            <button class="btn btn-outline-success w-100" onclick="exportAppointmentReport()">
                                <i class="fas fa-calendar me-2"></i>Export Appointment Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

function loadSystemSettingsData() {
    const settingsContainer = document.getElementById('systemSettingsContent');

    if (settingsContainer) {
        settingsContainer.innerHTML = `
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>System Configuration</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Hospital Name</label>
                                <input type="text" class="form-control" value="Newgate Hospital" id="hospitalName">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Contact Phone</label>
                                <input type="text" class="form-control" value="+234 ************" id="hospitalPhone">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Address</label>
                                <textarea class="form-control" id="hospitalAddress">Minna-Bida Road, Minna, Niger State</textarea>
                            </div>
                            <button class="btn btn-primary" onclick="saveSystemSettings()">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-database me-2"></i>Data Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Warning:</strong> These actions cannot be undone.
                            </div>
                            <button class="btn btn-outline-info w-100 mb-2" onclick="backupSystemData()">
                                <i class="fas fa-download me-2"></i>Backup System Data
                            </button>
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="clearOldData()">
                                <i class="fas fa-broom me-2"></i>Clear Old Data
                            </button>
                            <button class="btn btn-outline-danger w-100" onclick="resetSystem()">
                                <i class="fas fa-trash me-2"></i>Reset System
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// Action functions
function editUser(matricNumber) {
    showSuccessAlert(`Edit user functionality for ${matricNumber} - Feature coming soon!`);
}

function deleteUser(matricNumber) {
    if (confirm(`Are you sure you want to delete user ${matricNumber}?`)) {
        const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
        delete registeredPatients[matricNumber];
        localStorage.setItem('registeredPatients', JSON.stringify(registeredPatients));
        loadUserManagementData();
        showSuccessAlert(`User ${matricNumber} deleted successfully!`);
    }
}

function exportStudentReport() {
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const csvContent = generateStudentCSV(registeredPatients);
    downloadCSV(csvContent, 'student-report.csv');
    showSuccessAlert('Student report exported successfully!');
}

function exportAppointmentReport() {
    const stats = getSystemStats();
    const reportData = {
        totalStudents: stats.totalPatients,
        totalAppointments: stats.totalAppointments,
        exportDate: new Date().toISOString()
    };
    downloadJSON(reportData, 'appointment-report.json');
    showSuccessAlert('Appointment report exported successfully!');
}

function saveSystemSettings() {
    const settings = {
        hospitalName: document.getElementById('hospitalName').value,
        hospitalPhone: document.getElementById('hospitalPhone').value,
        hospitalAddress: document.getElementById('hospitalAddress').value,
        lastUpdated: new Date().toISOString()
    };
    localStorage.setItem('systemSettings', JSON.stringify(settings));
    showSuccessAlert('System settings saved successfully!');
}

function backupSystemData() {
    const allData = {
        registeredPatients: JSON.parse(localStorage.getItem('registeredPatients')) || {},
        systemSettings: JSON.parse(localStorage.getItem('systemSettings')) || {},
        backupDate: new Date().toISOString()
    };
    downloadJSON(allData, 'system-backup.json');
    showSuccessAlert('System data backed up successfully!');
}

function clearOldData() {
    if (confirm('Are you sure you want to clear old data? This will remove inactive records.')) {
        showSuccessAlert('Old data cleared successfully!');
    }
}

function resetSystem() {
    if (confirm('Are you sure you want to reset the entire system? This will delete ALL data!')) {
        if (confirm('This action cannot be undone. Are you absolutely sure?')) {
            localStorage.clear();
            location.reload();
        }
    }
}

// Utility functions
function generateStudentCSV(students) {
    let csv = 'Matric Number,Name,Email,Department,Phone,Registration Date\n';
    Object.keys(students).forEach(matricNumber => {
        const student = students[matricNumber];
        csv += `${matricNumber},"${student.name}","${student.email}","${student.department || 'N/A'}","${student.phone}","${student.registrationDate}"\n`;
    });
    return csv;
}

function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

function downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Profile and Settings Functions
function showAdminProfile() {
    const profileModal = `
        <div class="modal fade" id="adminProfileModal" tabindex="-1" aria-labelledby="adminProfileModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="adminProfileModalLabel">
                            <i class="fas fa-user me-2"></i>Administrator Profile
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="profile-avatar mb-3">
                                    <i class="fas fa-user-shield fa-5x text-primary"></i>
                                </div>
                                <h5 class="fw-bold">${currentAdmin?.name || 'Administrator'}</h5>
                                <p class="text-muted">${currentAdmin?.role || 'System Administrator'}</p>
                            </div>
                            <div class="col-md-8">
                                <h6 class="fw-bold mb-3">Profile Information</h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Admin ID</label>
                                        <input type="text" class="form-control" value="${currentAdmin?.id || 'ADMIN001'}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Full Name</label>
                                        <input type="text" class="form-control" value="${currentAdmin?.name || 'System Administrator'}" id="adminProfileName">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" value="${currentAdmin?.email || '<EMAIL>'}" id="adminProfileEmail">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Phone</label>
                                        <input type="tel" class="form-control" value="+234 ************" id="adminProfilePhone">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">Department</label>
                                        <input type="text" class="form-control" value="Information Technology" id="adminProfileDepartment">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">Last Login</label>
                                        <input type="text" class="form-control" value="${new Date().toLocaleString()}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="updateAdminProfile()">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                        <button type="button" class="btn btn-warning" onclick="changeAdminPassword()">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('adminProfileModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', profileModal);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('adminProfileModal'));
    modal.show();
}

function showAdminSettings() {
    const settingsModal = `
        <div class="modal fade" id="adminSettingsModal" tabindex="-1" aria-labelledby="adminSettingsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title" id="adminSettingsModalLabel">
                            <i class="fas fa-cog me-2"></i>Administrator Settings
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-4">
                            <div class="col-12">
                                <h6 class="fw-bold">Notification Preferences</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        Email Notifications
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                                    <label class="form-check-label" for="smsNotifications">
                                        SMS Notifications
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="systemAlerts" checked>
                                    <label class="form-check-label" for="systemAlerts">
                                        System Alerts
                                    </label>
                                </div>
                            </div>
                            <div class="col-12">
                                <h6 class="fw-bold">Dashboard Preferences</h6>
                                <div class="mb-3">
                                    <label class="form-label">Default Dashboard View</label>
                                    <select class="form-select" id="defaultDashboardView">
                                        <option value="overview">Overview</option>
                                        <option value="students">Students</option>
                                        <option value="appointments">Appointments</option>
                                        <option value="reports">Reports</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Items per Page</label>
                                    <select class="form-select" id="itemsPerPage">
                                        <option value="10">10</option>
                                        <option value="25" selected>25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <h6 class="fw-bold">Security Settings</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                                    <label class="form-check-label" for="twoFactorAuth">
                                        Two-Factor Authentication
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="sessionTimeout" checked>
                                    <label class="form-check-label" for="sessionTimeout">
                                        Auto Session Timeout (30 minutes)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="saveAdminSettings()">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('adminSettingsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', settingsModal);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('adminSettingsModal'));
    modal.show();
}

function updateAdminProfile() {
    const name = document.getElementById('adminProfileName').value;
    const email = document.getElementById('adminProfileEmail').value;
    const phone = document.getElementById('adminProfilePhone').value;
    const department = document.getElementById('adminProfileDepartment').value;

    // Update current admin data
    if (currentAdmin) {
        currentAdmin.name = name;
        currentAdmin.email = email;
        currentAdmin.phone = phone;
        currentAdmin.department = department;

        // Save to localStorage
        localStorage.setItem('hospitalUser', JSON.stringify(currentAdmin));

        // Update UI
        document.getElementById('adminName').textContent = name;
        document.getElementById('welcomeName').textContent = name;
    }

    showSuccessAlert('Profile updated successfully!');
    bootstrap.Modal.getInstance(document.getElementById('adminProfileModal')).hide();
}

function changeAdminPassword() {
    const passwordModal = `
        <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="changePasswordModalLabel">
                            <i class="fas fa-key me-2"></i>Change Password
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="changePasswordForm">
                            <div class="mb-3">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="currentPassword" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control" id="newPassword" required minlength="6">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" onclick="updateAdminPassword()">
                            <i class="fas fa-save me-2"></i>Update Password
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('changePasswordModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', passwordModal);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();
}

function updateAdminPassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (newPassword !== confirmPassword) {
        showErrorAlert('New passwords do not match!');
        return;
    }

    if (newPassword.length < 6) {
        showErrorAlert('Password must be at least 6 characters long!');
        return;
    }

    // In a real application, you would verify the current password
    showSuccessAlert('Password updated successfully!');
    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
    bootstrap.Modal.getInstance(document.getElementById('adminProfileModal')).hide();
}

function saveAdminSettings() {
    const settings = {
        emailNotifications: document.getElementById('emailNotifications').checked,
        smsNotifications: document.getElementById('smsNotifications').checked,
        systemAlerts: document.getElementById('systemAlerts').checked,
        defaultDashboardView: document.getElementById('defaultDashboardView').value,
        itemsPerPage: document.getElementById('itemsPerPage').value,
        twoFactorAuth: document.getElementById('twoFactorAuth').checked,
        sessionTimeout: document.getElementById('sessionTimeout').checked,
        lastUpdated: new Date().toISOString()
    };

    localStorage.setItem('adminSettings', JSON.stringify(settings));
    showSuccessAlert('Settings saved successfully!');
    bootstrap.Modal.getInstance(document.getElementById('adminSettingsModal')).hide();
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showDoctors = showDoctors;
window.showNurses = showNurses;
window.showPatients = showPatients;
window.showAppointments = showAppointments;
window.showUserManagement = showUserManagement;
window.showReports = showReports;
window.showSystemSettings = showSystemSettings;
window.showAdminProfile = showAdminProfile;
window.showAdminSettings = showAdminSettings;
window.updateAdminProfile = updateAdminProfile;
window.changeAdminPassword = changeAdminPassword;
window.updateAdminPassword = updateAdminPassword;
window.saveAdminSettings = saveAdminSettings;
window.loadUserManagementData = loadUserManagementData;
window.loadReportsData = loadReportsData;
window.loadSystemSettingsData = loadSystemSettingsData;
window.loadDoctorsData = loadDoctorsData;
window.loadNursesData = loadNursesData;
window.loadPatientsData = loadPatientsData;
window.loadAppointmentsData = loadAppointmentsData;
window.editUser = editUser;
window.deleteUser = deleteUser;
window.exportStudentReport = exportStudentReport;
window.exportAppointmentReport = exportAppointmentReport;
window.saveSystemSettings = saveSystemSettings;
window.backupSystemData = backupSystemData;
window.clearOldData = clearOldData;
window.resetSystem = resetSystem;
