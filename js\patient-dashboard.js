// Patient Dashboard JavaScript

let currentPatient = null;

document.addEventListener('DOMContentLoaded', function() {
    initializePatientDashboard();
});

function initializePatientDashboard() {
    // Check authentication
    if (!checkPatientAuthentication()) {
        return;
    }
    
    // Load patient data
    loadPatientData();

    // Initialize profile picture manager
    profilePictureManager.initializeProfilePicture(currentPatient.matricNumber, 'student');

    // Initialize messaging system
    messagingUI.initialize({
        id: currentPatient.matricNumber,
        name: currentPatient.name,
        role: 'patient'
    });

    // Initialize sidebar navigation
    initializeSidebarNavigation();

    // Set minimum date for appointment booking
    setMinimumAppointmentDate();
}

function checkPatientAuthentication() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (!userData) {
        redirectToLogin();
        return false;
    }
    
    const user = JSON.parse(userData);
    if (user.role !== 'patient') {
        showErrorAlert('Access denied. Patient login required.');
        redirectToLogin();
        return false;
    }
    
    currentPatient = user;
    return true;
}

function redirectToLogin() {
    setTimeout(() => {
        window.location.href = 'patient-login.html';
    }, 2000);
}

function loadPatientData() {
    if (currentPatient) {
        console.log('Loading patient data:', currentPatient);

        // Update welcome message and basic info
        const patientNameEl = document.getElementById('patientName');
        const welcomeNameEl = document.getElementById('welcomeName');
        const patientIdEl = document.getElementById('patientId');
        const lastLoginEl = document.getElementById('lastLogin');

        if (patientNameEl) patientNameEl.textContent = currentPatient.name;
        if (welcomeNameEl) welcomeNameEl.textContent = currentPatient.name;
        if (patientIdEl) patientIdEl.textContent = currentPatient.id;

        // Update medical records with actual patient data
        const recordNameEl = document.getElementById('recordName');
        const recordMatricEl = document.getElementById('recordMatricNumber');
        const recordEmailEl = document.getElementById('recordEmail');
        const recordPhoneEl = document.getElementById('recordPhone');
        const recordDOBEl = document.getElementById('recordDOB');

        if (recordNameEl) recordNameEl.textContent = currentPatient.name;
        if (recordMatricEl) recordMatricEl.textContent = currentPatient.matricNumber || currentPatient.id;
        if (recordEmailEl) recordEmailEl.textContent = currentPatient.email;
        if (recordPhoneEl) recordPhoneEl.textContent = currentPatient.phone;

        // Format and display date of birth
        if (recordDOBEl && currentPatient.dateOfBirth) {
            const dob = new Date(currentPatient.dateOfBirth);
            recordDOBEl.textContent = dob.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Update additional patient information if available
        updateAdditionalPatientInfo();

        // Load personalized medical data
        loadPersonalizedMedicalData();

        // Load billing and insurance data
        loadBillingAndInsuranceData();

        // Format last login time
        if (lastLoginEl && currentPatient.loginTime) {
            const loginTime = new Date(currentPatient.loginTime);
            lastLoginEl.textContent = formatDate(loginTime);
        }
    }
}

function updateAdditionalPatientInfo() {
    // Update gender if element exists
    const genderEl = document.getElementById('recordGender');
    if (genderEl && currentPatient.gender) {
        genderEl.textContent = capitalizeFirst(currentPatient.gender);
    }

    // Update blood group if element exists
    const bloodGroupEl = document.getElementById('recordBloodGroup');
    if (bloodGroupEl && currentPatient.bloodGroup) {
        bloodGroupEl.textContent = currentPatient.bloodGroup;
    }

    // Update department if element exists
    const departmentEl = document.getElementById('recordDepartment');
    if (departmentEl && currentPatient.department) {
        departmentEl.textContent = getDepartmentFullName(currentPatient.department);
    }

    // Update address information
    const addressEl = document.getElementById('recordAddress');
    if (addressEl && currentPatient.address) {
        const fullAddress = `${currentPatient.address}, ${currentPatient.city || ''}, ${currentPatient.state || ''}`.replace(/,\s*,/g, ',').replace(/,\s*$/, '');
        addressEl.textContent = fullAddress;
    }

    // Update emergency contact information
    const emergencyContactEl = document.getElementById('recordEmergencyContact');
    if (emergencyContactEl && currentPatient.emergencyContactName) {
        emergencyContactEl.textContent = `${currentPatient.emergencyContactName} - ${currentPatient.emergencyContactPhone || 'No phone'}`;
    }

    // Update emergency contact relation
    const emergencyRelationEl = document.getElementById('recordEmergencyRelation');
    if (emergencyRelationEl && currentPatient.emergencyContactRelation) {
        emergencyRelationEl.textContent = capitalizeFirst(currentPatient.emergencyContactRelation);
    }

    // Update registration date if available
    const registrationDateEl = document.getElementById('recordRegistrationDate');
    if (registrationDateEl && currentPatient.registrationDate) {
        const regDate = new Date(currentPatient.registrationDate);
        registrationDateEl.textContent = regDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

// Utility function to capitalize first letter
function capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

// Function to get department full name
function getDepartmentFullName(deptCode) {
    const departments = {
        'BSE': 'Biomedical Science and Engineering',
        'BNS': 'Basic and Applied Sciences',
        'LLB': 'Law',
        'ENG': 'Engineering',
        'EDU': 'Education',
        'AGR': 'Agriculture',
        'ENV': 'Environmental Sciences',
        'ICT': 'Information and Communication Technology'
    };
    return departments[deptCode] || deptCode;
}

function loadPersonalizedMedicalData() {
    // Load or create personalized medical history for the patient
    const patientMedicalData = getPatientMedicalData(currentPatient.id);

    // Update dashboard stats
    updateDashboardStats(patientMedicalData);

    // Update recent medical visits
    updateRecentMedicalVisits(patientMedicalData.visits);

    // Update appointments
    updatePatientAppointments(patientMedicalData.appointments);

    // Update prescriptions
    updatePatientPrescriptions(patientMedicalData.prescriptions);

    // Update lab results
    updatePatientLabResults(patientMedicalData.labResults);

    // Update recent activity
    updateRecentActivity(patientMedicalData);

    // Update next appointment
    updateNextAppointment(patientMedicalData);
}

function updateDashboardStats(medicalData) {
    // Update upcoming appointments count
    const upcomingAppointments = medicalData.appointments.filter(apt => apt.status === 'scheduled').length;
    const appointmentsEl = document.getElementById('upcomingAppointments');
    if (appointmentsEl) {
        appointmentsEl.textContent = upcomingAppointments;
    }

    // Update active prescriptions count
    const activePrescriptions = medicalData.prescriptions.filter(rx => rx.status === 'active').length;
    const prescriptionsEl = document.getElementById('activePrescriptions');
    if (prescriptionsEl) {
        prescriptionsEl.textContent = activePrescriptions;
    }

    // Update lab results count
    const recentLabResults = medicalData.labResults.filter(lab => {
        const labDate = new Date(lab.date);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return labDate >= thirtyDaysAgo;
    }).length;
    const labResultsEl = document.getElementById('recentLabResults');
    if (labResultsEl) {
        labResultsEl.textContent = recentLabResults;
    }

    // Update account status
    const statusEl = document.getElementById('accountStatus');
    if (statusEl) {
        statusEl.textContent = currentPatient.status || 'Active';
    }
}

function getPatientMedicalData(patientId) {
    // Get stored medical data or create default data for new patients
    const storedData = localStorage.getItem(`medicalData_${patientId}`);

    if (storedData) {
        return JSON.parse(storedData);
    }

    // Create default medical data for new patients
    const defaultData = {
        visits: [
            {
                date: new Date().toISOString().split('T')[0],
                doctor: 'Dr. Welcome Team',
                diagnosis: 'Initial Registration',
                treatment: 'Welcome to Newgate Hospital! Your account has been created successfully.',
                status: 'completed'
            }
        ],
        appointments: [
            {
                date: getNextWeekDate(),
                time: '10:00 AM',
                doctor: 'Dr. General Practice',
                department: 'General Medicine',
                reason: 'Initial consultation and health assessment',
                status: 'scheduled'
            }
        ],
        prescriptions: [
            {
                id: 'RX001',
                medication: 'Multivitamin',
                doctor: 'Dr. Welcome Team',
                date: new Date().toISOString().split('T')[0],
                instructions: 'Take one tablet daily with food',
                status: 'active',
                refills: 5
            }
        ],
        labResults: [
            {
                id: 'LAB001',
                testName: 'Basic Health Panel',
                date: new Date().toISOString().split('T')[0],
                doctor: 'Dr. Welcome Team',
                status: 'normal',
                results: 'All values within normal range'
            }
        ]
    };

    // Save default data
    localStorage.setItem(`medicalData_${patientId}`, JSON.stringify(defaultData));
    return defaultData;
}

function updateRecentMedicalVisits(visits) {
    const tbody = document.querySelector('#medicalRecordsContent tbody');
    if (!tbody) return;

    if (visits.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No medical visits recorded yet.
                    <br><small>Your medical history will appear here after your first appointment.</small>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = visits.map(visit => `
        <tr>
            <td>${formatDate(visit.date)}</td>
            <td>${visit.doctor}</td>
            <td>${visit.diagnosis}</td>
            <td>${visit.treatment}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i> View Details
                </button>
            </td>
        </tr>
    `).join('');
}

function updatePatientAppointments(appointments) {
    const upcomingTable = document.getElementById('upcomingAppointmentsTable');
    if (!upcomingTable) return;

    const upcomingAppointments = appointments.filter(apt => apt.status === 'scheduled');

    if (upcomingAppointments.length === 0) {
        upcomingTable.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted">
                    <i class="fas fa-calendar-plus me-2"></i>No upcoming appointments.
                    <br><small>Click "Book New Appointment" to schedule your first visit.</small>
                </td>
            </tr>
        `;
        return;
    }

    upcomingTable.innerHTML = upcomingAppointments.map(apt => `
        <tr>
            <td>${formatDate(apt.date)} - ${apt.time}</td>
            <td>${apt.doctor}</td>
            <td>${apt.department}</td>
            <td><span class="badge bg-success">Confirmed</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-times"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function updatePatientPrescriptions(prescriptions) {
    console.log('Loading prescriptions for patient:', currentPatient.id);

    // Update current prescriptions section
    const currentPrescriptionsContainer = document.querySelector('#prescriptionsContent .row .col-12 .card-body .row');
    if (currentPrescriptionsContainer) {
        if (prescriptions.length === 0) {
            currentPrescriptionsContainer.innerHTML = `
                <div class="col-12">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-pills fa-3x mb-3"></i>
                        <h5>No Current Prescriptions</h5>
                        <p>You don't have any active prescriptions at the moment.</p>
                        <small>Prescriptions from your doctor will appear here.</small>
                    </div>
                </div>
            `;
        } else {
            currentPrescriptionsContainer.innerHTML = prescriptions.map(prescription => `
                <div class="col-lg-6 mb-3">
                    <div class="prescription-card border rounded p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="fw-bold text-primary">${prescription.medication}</h6>
                            <span class="badge bg-${prescription.status === 'active' ? 'success' : 'warning'}">${prescription.status}</span>
                        </div>
                        <p class="mb-1"><strong>Prescribed by:</strong> ${prescription.doctor}</p>
                        <p class="mb-1"><strong>Date:</strong> ${formatDate(prescription.date)}</p>
                        <p class="mb-1"><strong>Instructions:</strong> ${prescription.instructions}</p>
                        <p class="mb-1"><strong>Refills remaining:</strong> ${prescription.refills || 0}</p>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="requestRefill('${prescription.id}')">
                                <i class="fas fa-redo me-1"></i>Request Refill
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="downloadPrescription('${prescription.id}')">
                                <i class="fas fa-download me-1"></i>Download
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    }

    // Update prescription history table
    const prescriptionHistoryTable = document.querySelector('#prescriptionsContent .card:last-child tbody');
    if (prescriptionHistoryTable) {
        const completedPrescriptions = prescriptions.filter(rx => rx.status === 'completed');

        if (completedPrescriptions.length === 0) {
            prescriptionHistoryTable.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted py-4">
                        <i class="fas fa-history me-2"></i>No prescription history available.
                        <br><small>Your completed prescriptions will appear here.</small>
                    </td>
                </tr>
            `;
        } else {
            prescriptionHistoryTable.innerHTML = completedPrescriptions.map(rx => `
                <tr>
                    <td>${rx.medication}</td>
                    <td>${rx.doctor}</td>
                    <td>${formatDate(rx.date)}</td>
                    <td><span class="badge bg-secondary">Completed</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-info" onclick="viewPrescription('${rx.id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }
}

function updatePatientLabResults(labResults) {
    console.log('Loading lab results for patient:', currentPatient.id);

    // Update recent lab results section
    const recentLabContainer = document.querySelector('#labResultsContent .row .col-12 .card-body .row');
    if (recentLabContainer) {
        const recentResults = labResults.filter(lab => {
            const labDate = new Date(lab.date);
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
            return labDate >= sixMonthsAgo;
        });

        if (recentResults.length === 0) {
            recentLabContainer.innerHTML = `
                <div class="col-12">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-vial fa-3x mb-3"></i>
                        <h5>No Recent Lab Results</h5>
                        <p>You don't have any recent laboratory test results.</p>
                        <small>Lab results from your tests will appear here.</small>
                    </div>
                </div>
            `;
        } else {
            recentLabContainer.innerHTML = recentResults.map(lab => `
                <div class="col-lg-6 mb-3">
                    <div class="lab-result-card border rounded p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="fw-bold text-primary">${lab.testName}</h6>
                            <span class="badge bg-${lab.status === 'normal' ? 'success' : lab.status === 'abnormal' ? 'danger' : 'warning'}">${lab.status}</span>
                        </div>
                        <p class="mb-1"><strong>Date:</strong> ${formatDate(lab.date)}</p>
                        <p class="mb-1"><strong>Ordered by:</strong> ${lab.doctor}</p>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Key Results:</strong><br>
                                ${lab.results || 'Results available in full report'}
                            </small>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="viewLabResult('${lab.id}')">
                                <i class="fas fa-eye me-1"></i>View Full Report
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="downloadLabResult('${lab.id}')">
                                <i class="fas fa-download me-1"></i>Download PDF
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    }

    // Update lab history table
    const labHistoryTable = document.querySelector('#labResultsContent .card:last-child tbody');
    if (labHistoryTable) {
        if (labResults.length === 0) {
            labHistoryTable.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-history me-2"></i>No lab test history available.
                        <br><small>Your laboratory test history will appear here.</small>
                    </td>
                </tr>
            `;
        } else {
            labHistoryTable.innerHTML = labResults.map(lab => `
                <tr>
                    <td>${lab.testName}</td>
                    <td>${formatDate(lab.date)}</td>
                    <td>${lab.doctor}</td>
                    <td><span class="badge bg-success">Completed</span></td>
                    <td><span class="badge bg-${lab.status === 'normal' ? 'success' : lab.status === 'abnormal' ? 'danger' : 'warning'}">${lab.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewLabResult('${lab.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadLabResult('${lab.id}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }
}

function getNextWeekDate() {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    return nextWeek.toISOString().split('T')[0];
}

function updateRecentActivity(medicalData) {
    const activityContainer = document.getElementById('recentActivity');
    if (!activityContainer) return;

    // Create activity timeline based on patient data
    const activities = [];

    // Add registration activity
    if (currentPatient.registrationDate) {
        activities.push({
            date: currentPatient.registrationDate,
            icon: 'fas fa-user-plus',
            color: 'success',
            title: 'Account Created',
            description: 'Welcome to Newgate Hospital! Your patient account has been successfully created.'
        });
    }

    // Add login activity
    if (currentPatient.loginTime) {
        activities.push({
            date: currentPatient.loginTime,
            icon: 'fas fa-sign-in-alt',
            color: 'primary',
            title: 'Logged In',
            description: 'You logged into your patient portal.'
        });
    }

    // Add recent appointments
    medicalData.appointments.forEach(apt => {
        if (apt.status === 'scheduled') {
            activities.push({
                date: apt.date,
                icon: 'fas fa-calendar-check',
                color: 'info',
                title: 'Appointment Scheduled',
                description: `Appointment with ${apt.doctor} in ${apt.department}`
            });
        }
    });

    // Sort activities by date (most recent first)
    activities.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Take only the 5 most recent activities
    const recentActivities = activities.slice(0, 5);

    if (recentActivities.length === 0) {
        activityContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <p>No recent activity</p>
                <small>Your activities will appear here as you use the system.</small>
            </div>
        `;
        return;
    }

    activityContainer.innerHTML = recentActivities.map(activity => `
        <div class="d-flex align-items-start mb-3">
            <div class="flex-shrink-0">
                <div class="rounded-circle bg-${activity.color} text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="${activity.icon}"></i>
                </div>
            </div>
            <div class="flex-grow-1 ms-3">
                <h6 class="mb-1">${activity.title}</h6>
                <p class="text-muted mb-1">${activity.description}</p>
                <small class="text-muted">${formatDate(activity.date)}</small>
            </div>
        </div>
    `).join('');
}

function updateNextAppointment(medicalData) {
    const nextAppointmentContainer = document.getElementById('nextAppointment');
    if (!nextAppointmentContainer) return;

    const upcomingAppointments = medicalData.appointments.filter(apt => apt.status === 'scheduled');

    if (upcomingAppointments.length === 0) {
        nextAppointmentContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-calendar-times fa-2x mb-3"></i>
                <p>No upcoming appointments</p>
                <button class="btn btn-primary btn-sm" onclick="bookNewAppointment()">
                    Book Now
                </button>
            </div>
        `;
        return;
    }

    const nextAppointment = upcomingAppointments[0];
    nextAppointmentContainer.innerHTML = `
        <div class="text-center">
            <div class="mb-3">
                <i class="fas fa-calendar-check fa-2x text-primary"></i>
            </div>
            <h6 class="fw-bold">${nextAppointment.doctor}</h6>
            <p class="text-muted mb-2">${nextAppointment.department}</p>
            <p class="mb-2"><strong>${formatDate(nextAppointment.date)}</strong></p>
            <p class="mb-3"><strong>${nextAppointment.time}</strong></p>
            <button class="btn btn-outline-primary btn-sm" onclick="showAppointments()">
                View Details
            </button>
        </div>
    `;
}

function loadBillingAndInsuranceData() {
    // Load or create billing and insurance data for the patient
    const billingData = getPatientBillingData(currentPatient.id);
    const insuranceData = getPatientInsuranceData(currentPatient.id);

    // Update billing information
    updateBillingInformation(billingData);

    // Update insurance information
    updateInsuranceInformation(insuranceData);
}

function getPatientBillingData(patientId) {
    // Get stored billing data or create default data
    const storedData = localStorage.getItem(`billingData_${patientId}`);

    if (storedData) {
        return JSON.parse(storedData);
    }

    // Create default billing data for new patients
    const defaultData = {
        currentBalance: 0,
        recentCharges: [
            {
                id: 'REG001',
                date: new Date().toISOString().split('T')[0],
                description: 'Registration Fee',
                amount: 5000,
                status: 'paid',
                category: 'Registration'
            }
        ],
        paymentHistory: [
            {
                id: 'PAY001',
                date: new Date().toISOString().split('T')[0],
                amount: 5000,
                method: 'Registration Waived',
                status: 'completed',
                description: 'Welcome registration - No charge'
            }
        ],
        totalPaid: 5000,
        totalCharges: 5000
    };

    // Save default data
    localStorage.setItem(`billingData_${patientId}`, JSON.stringify(defaultData));
    return defaultData;
}

function getPatientInsuranceData(patientId) {
    // Get insurance data from patient registration or create default
    const registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};
    const patientInfo = registeredPatients[patientId];

    if (patientInfo && patientInfo.insuranceProvider) {
        return {
            provider: patientInfo.insuranceProvider,
            policyNumber: patientInfo.policyNumber || 'Not provided',
            status: 'active',
            coverage: 'Basic Coverage',
            copay: '₦2,000',
            deductible: '₦50,000',
            lastVerified: new Date().toISOString().split('T')[0]
        };
    }

    // Default insurance data for patients without insurance
    return {
        provider: 'No Insurance',
        policyNumber: 'N/A',
        status: 'uninsured',
        coverage: 'Self-Pay',
        copay: 'Full Payment',
        deductible: 'N/A',
        lastVerified: 'N/A'
    };
}

function updateBillingInformation(billingData) {
    // Update current balance
    const balanceEl = document.getElementById('currentBalance');
    if (balanceEl) {
        balanceEl.textContent = `₦${billingData.currentBalance.toLocaleString()}`;
        balanceEl.className = billingData.currentBalance > 0 ? 'text-danger fw-bold' : 'text-success fw-bold';
    }

    // Update total charges
    const totalChargesEl = document.getElementById('totalCharges');
    if (totalChargesEl) {
        totalChargesEl.textContent = `₦${billingData.totalCharges.toLocaleString()}`;
    }

    // Update total paid
    const totalPaidEl = document.getElementById('totalPaid');
    if (totalPaidEl) {
        totalPaidEl.textContent = `₦${billingData.totalPaid.toLocaleString()}`;
    }

    // Update recent charges table
    const recentChargesTable = document.querySelector('#billingContent .card:first-child tbody');
    if (recentChargesTable) {
        if (billingData.recentCharges.length === 0) {
            recentChargesTable.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted py-4">
                        <i class="fas fa-file-invoice me-2"></i>No recent charges.
                        <br><small>Your billing charges will appear here.</small>
                    </td>
                </tr>
            `;
        } else {
            recentChargesTable.innerHTML = billingData.recentCharges.map(charge => `
                <tr>
                    <td>${formatDate(charge.date)}</td>
                    <td>${charge.description}</td>
                    <td>${charge.category}</td>
                    <td>₦${charge.amount.toLocaleString()}</td>
                    <td><span class="badge bg-${charge.status === 'paid' ? 'success' : 'warning'}">${charge.status}</span></td>
                </tr>
            `).join('');
        }
    }

    // Update payment history table
    const paymentHistoryTable = document.querySelector('#billingContent .card:last-child tbody');
    if (paymentHistoryTable) {
        if (billingData.paymentHistory.length === 0) {
            paymentHistoryTable.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted py-4">
                        <i class="fas fa-credit-card me-2"></i>No payment history.
                        <br><small>Your payment history will appear here.</small>
                    </td>
                </tr>
            `;
        } else {
            paymentHistoryTable.innerHTML = billingData.paymentHistory.map(payment => `
                <tr>
                    <td>${formatDate(payment.date)}</td>
                    <td>₦${payment.amount.toLocaleString()}</td>
                    <td>${payment.method}</td>
                    <td><span class="badge bg-success">Completed</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadReceipt('${payment.id}')">
                            <i class="fas fa-download"></i> Receipt
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }
}

function updateInsuranceInformation(insuranceData) {
    // Update insurance provider
    const providerEl = document.getElementById('insuranceProvider');
    if (providerEl) {
        providerEl.textContent = insuranceData.provider;
    }

    // Update policy number
    const policyEl = document.getElementById('policyNumber');
    if (policyEl) {
        policyEl.textContent = insuranceData.policyNumber;
    }

    // Update insurance status
    const statusEl = document.getElementById('insuranceStatus');
    if (statusEl) {
        statusEl.innerHTML = `<span class="badge bg-${insuranceData.status === 'active' ? 'success' : 'warning'}">${insuranceData.status}</span>`;
    }

    // Update coverage details
    const coverageEl = document.getElementById('coverageDetails');
    if (coverageEl) {
        coverageEl.innerHTML = `
            <p><strong>Coverage Type:</strong> ${insuranceData.coverage}</p>
            <p><strong>Copay:</strong> ${insuranceData.copay}</p>
            <p><strong>Deductible:</strong> ${insuranceData.deductible}</p>
            <p><strong>Last Verified:</strong> ${insuranceData.lastVerified !== 'N/A' ? formatDate(insuranceData.lastVerified) : 'N/A'}</p>
        `;
    }
}

function initializeSidebarNavigation() {
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            sidebarLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
        });
    });
}

function setMinimumAppointmentDate() {
    const dateInput = document.getElementById('appointmentDate');
    if (dateInput) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        dateInput.min = tomorrow.toISOString().split('T')[0];
    }
}

// Navigation functions
function showDashboard() {
    hideAllContentSections();
    document.getElementById('dashboardContent').classList.remove('d-none');
    showSuccessAlert('Dashboard loaded successfully');
}

function showAppointments() {
    hideAllContentSections();
    document.getElementById('appointmentsContent').classList.remove('d-none');
    showSuccessAlert('Appointments section loaded');
}

function showMedicalRecords() {
    hideAllContentSections();
    document.getElementById('medicalRecordsContent').classList.remove('d-none');
    showSuccessAlert('Medical records loaded');
}

function showPrescriptions() {
    hideAllContentSections();
    document.getElementById('prescriptionsContent').classList.remove('d-none');
    showSuccessAlert('Prescriptions loaded');
}

function showLabResults() {
    hideAllContentSections();
    document.getElementById('labResultsContent').classList.remove('d-none');
    showSuccessAlert('Lab results loaded');
}

function showBilling() {
    hideAllContentSections();
    document.getElementById('billingContent').classList.remove('d-none');
    showSuccessAlert('Billing information loaded');
}

function showInsurance() {
    hideAllContentSections();
    document.getElementById('insuranceContent').classList.remove('d-none');
    showSuccessAlert('Insurance information loaded');
}

function hideAllContentSections() {
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.add('d-none');
    });
}

// Appointment booking function
function submitAppointment() {
    const form = document.getElementById('bookAppointmentForm');
    const formData = new FormData(form);
    
    const appointmentData = {
        department: formData.get('appointmentDepartment'),
        doctor: formData.get('appointmentDoctor'),
        date: formData.get('appointmentDate'),
        time: formData.get('appointmentTime'),
        reason: formData.get('appointmentReason'),
        notes: formData.get('appointmentNotes')
    };
    
    // Validate required fields
    if (!appointmentData.department || !appointmentData.date || !appointmentData.time || !appointmentData.reason) {
        showErrorAlert('Please fill in all required fields.');
        return;
    }
    
    // Simulate appointment booking
    showSuccessAlert('Appointment request submitted successfully! You will receive a confirmation email shortly.');
    
    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('bookAppointmentModal'));
    modal.hide();
    form.reset();
    
    // Refresh appointments section if it's currently visible
    if (!document.getElementById('appointmentsContent').classList.contains('d-none')) {
        showAppointments();
    }
}

// Utility functions
function formatDate(dateString) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

function showSuccessAlert(message) {
    console.log('Success:', message);
    // You can implement a toast notification system here
}

function showErrorAlert(message) {
    console.log('Error:', message);
    // You can implement a toast notification system here
}

function logout() {
    // Clear user data
    localStorage.removeItem('hospitalUser');
    sessionStorage.clear();
    
    // Show success message
    showSuccessAlert('Logged out successfully!');
    
    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Quick Action Functions
function bookNewAppointment() {
    // Show the book appointment modal
    const modal = new bootstrap.Modal(document.getElementById('bookAppointmentModal'));
    modal.show();
    showSuccessAlert('Opening appointment booking form...');
}

function viewMedicalRecords() {
    // Navigate to medical records section
    showMedicalRecords();

    // Update sidebar active state
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => link.classList.remove('active'));
    document.querySelector('a[onclick="showMedicalRecords()"]').classList.add('active');
}

function downloadReports() {
    // Create a modal for report selection
    showReportDownloadModal();
}

function contactSupport() {
    // Show contact support modal
    showContactSupportModal();
}

function showReportDownloadModal() {
    // Create and show a modal for downloading reports
    const modalHTML = `
        <div class="modal fade" id="downloadReportsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-download me-2"></i>Download Reports
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-3">Select the reports you want to download:</p>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action" onclick="downloadReport('medical-summary')">
                                <i class="fas fa-file-medical me-2 text-primary"></i>
                                <strong>Medical Summary Report</strong>
                                <small class="d-block text-muted">Complete medical history and current status</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="downloadReport('lab-results')">
                                <i class="fas fa-vial me-2 text-success"></i>
                                <strong>Latest Lab Results</strong>
                                <small class="d-block text-muted">Recent laboratory test results</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="downloadReport('prescriptions')">
                                <i class="fas fa-pills me-2 text-warning"></i>
                                <strong>Current Prescriptions</strong>
                                <small class="d-block text-muted">Active medications and dosages</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="downloadReport('billing')">
                                <i class="fas fa-file-invoice-dollar me-2 text-danger"></i>
                                <strong>Billing Statement</strong>
                                <small class="d-block text-muted">Recent charges and payment history</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="downloadReport('insurance')">
                                <i class="fas fa-shield-alt me-2 text-info"></i>
                                <strong>Insurance Summary</strong>
                                <small class="d-block text-muted">Coverage details and claims history</small>
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-info" onclick="downloadAllReports()">
                            <i class="fas fa-download me-2"></i>Download All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('downloadReportsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('downloadReportsModal'));
    modal.show();
}

function showContactSupportModal() {
    // Create and show a modal for contacting support
    const modalHTML = `
        <div class="modal fade" id="contactSupportModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-headset me-2"></i>Contact Support
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-phone me-2 text-primary"></i>Phone Support</h6>
                                <p class="mb-3">
                                    <strong>Emergency:</strong> +234-800-NEWGATE<br>
                                    <strong>General Support:</strong> +234-801-SUPPORT<br>
                                    <strong>Hours:</strong> 24/7 Emergency, 8AM-6PM General
                                </p>

                                <h6><i class="fas fa-envelope me-2 text-success"></i>Email Support</h6>
                                <p class="mb-3">
                                    <strong>General:</strong> <EMAIL><br>
                                    <strong>Billing:</strong> <EMAIL><br>
                                    <strong>Technical:</strong> <EMAIL>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-comment me-2 text-info"></i>Send Message</h6>
                                <form id="supportMessageForm">
                                    <div class="mb-3">
                                        <label for="supportCategory" class="form-label">Category</label>
                                        <select class="form-select" id="supportCategory" required>
                                            <option value="">Select Category</option>
                                            <option value="appointment">Appointment Issue</option>
                                            <option value="billing">Billing Question</option>
                                            <option value="technical">Technical Problem</option>
                                            <option value="medical">Medical Records</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="supportMessage" class="form-label">Message</label>
                                        <textarea class="form-control" id="supportMessage" rows="4" required placeholder="Please describe your issue or question..."></textarea>
                                    </div>
                                    <button type="button" class="btn btn-warning w-100" onclick="sendSupportMessage()">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('contactSupportModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('contactSupportModal'));
    modal.show();
}

function downloadReport(reportType) {
    // Simulate report download
    const reportNames = {
        'medical-summary': 'Medical Summary Report',
        'lab-results': 'Latest Lab Results',
        'prescriptions': 'Current Prescriptions',
        'billing': 'Billing Statement',
        'insurance': 'Insurance Summary'
    };

    const reportName = reportNames[reportType] || 'Report';
    showSuccessAlert(`Downloading ${reportName}... The file will be saved to your downloads folder.`);

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('downloadReportsModal'));
    if (modal) {
        modal.hide();
    }

    // Simulate file download (in a real app, this would trigger an actual download)
    setTimeout(() => {
        showSuccessAlert(`${reportName} downloaded successfully!`);
    }, 2000);
}

function downloadAllReports() {
    showSuccessAlert('Preparing all reports for download... This may take a few moments.');

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('downloadReportsModal'));
    if (modal) {
        modal.hide();
    }

    // Simulate downloading all reports
    setTimeout(() => {
        showSuccessAlert('All reports have been downloaded successfully!');
    }, 3000);
}

function sendSupportMessage() {
    const category = document.getElementById('supportCategory').value;
    const message = document.getElementById('supportMessage').value;

    if (!category || !message.trim()) {
        showErrorAlert('Please select a category and enter your message.');
        return;
    }

    // Simulate sending message
    showSuccessAlert('Your message has been sent successfully! Our support team will respond within 24 hours.');

    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('contactSupportModal'));
    if (modal) {
        modal.hide();
    }

    // Reset form
    document.getElementById('supportMessageForm').reset();
}

// Enhanced alert functions with better UI feedback
function showSuccessAlert(message) {
    // Create a toast notification
    const toastHTML = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement, { delay: 4000 });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function showErrorAlert(message) {
    // Create a toast notification
    const toastHTML = `
        <div class="toast align-items-center text-white bg-danger border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement, { delay: 4000 });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// Action functions for prescriptions and lab results
function requestRefill(prescriptionId) {
    showSuccessAlert(`Refill request submitted for prescription ${prescriptionId}. Your doctor will review and approve.`);
}

function downloadPrescription(prescriptionId) {
    showSuccessAlert(`Downloading prescription ${prescriptionId}...`);
}

function viewPrescription(prescriptionId) {
    showSuccessAlert(`Opening detailed view for prescription ${prescriptionId}...`);
}

function viewLabResult(labId) {
    showSuccessAlert(`Opening detailed lab result ${labId}...`);
}

function downloadLabResult(labId) {
    showSuccessAlert(`Downloading lab result ${labId} as PDF...`);
}

function downloadReceipt(paymentId) {
    showSuccessAlert(`Downloading receipt for payment ${paymentId}...`);
}

// Make functions globally available
window.showDashboard = showDashboard;
window.showAppointments = showAppointments;
window.showMedicalRecords = showMedicalRecords;
window.showPrescriptions = showPrescriptions;
window.showLabResults = showLabResults;
window.showBilling = showBilling;
window.showInsurance = showInsurance;
window.submitAppointment = submitAppointment;
window.logout = logout;

// Quick Actions
window.bookNewAppointment = bookNewAppointment;
window.viewMedicalRecords = viewMedicalRecords;
window.downloadReports = downloadReports;
window.contactSupport = contactSupport;
window.downloadReport = downloadReport;
window.downloadAllReports = downloadAllReports;
window.sendSupportMessage = sendSupportMessage;

// Prescription and Lab Actions
window.requestRefill = requestRefill;
window.downloadPrescription = downloadPrescription;
window.viewPrescription = viewPrescription;
window.viewLabResult = viewLabResult;
window.downloadLabResult = downloadLabResult;
window.downloadReceipt = downloadReceipt;
