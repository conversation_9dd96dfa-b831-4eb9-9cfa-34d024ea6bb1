// Nurse Login System - Newgate Hospital Management System

// Pre-configured nurse accounts
const nurseAccounts = {
    'NUR001': {
        id: 'NUR001',
        password: 'nurse123',
        name: 'Nurse <PERSON><PERSON>',
        department: 'General Ward',
        email: 'amina.<PERSON><PERSON><PERSON>@newgate.edu.ng',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'RN-001-2021',
        experience: '5 years',
        education: 'BSc Nursing, University of Lagos',
        role: 'nurse',
        status: 'active',
        lastLogin: null,
        specialization: 'General Nursing'
    },
    'NUR002': {
        id: 'NUR002',
        password: 'nurse123',
        name: 'Nurse Blessing <PERSON>',
        department: 'Pediatric Ward',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Night Shift',
        licenseNumber: 'RN-002-2020',
        experience: '7 years',
        education: 'BSc Nursing, University of Ibadan',
        role: 'nurse',
        status: 'active',
        lastLogin: null,
        specialization: 'Pediatric Nursing'
    },
    'NUR003': {
        id: 'NUR003',
        password: 'nurse123',
        name: 'Nurse <PERSON>',
        department: 'Emergency Ward',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'RN-003-2019',
        experience: '9 years',
        education: 'BSc Nursing, Ahmadu Bello University',
        role: 'nurse',
        status: 'active',
        lastLogin: null,
        specialization: 'Emergency Nursing'
    },
    'NUR004': {
        id: 'NUR004',
        password: 'nurse123',
        name: 'Nurse Mary Johnson',
        department: 'Surgical Ward',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Day Shift',
        licenseNumber: 'RN-004-2022',
        experience: '3 years',
        education: 'BSc Nursing, University of Maiduguri',
        role: 'nurse',
        status: 'active',
        lastLogin: null,
        specialization: 'Surgical Nursing'
    },
    'NUR005': {
        id: 'NUR005',
        password: 'nurse123',
        name: 'Nurse Khadija Usman',
        department: 'Maternity Ward',
        email: '<EMAIL>',
        phone: '+234 ************',
        shift: 'Night Shift',
        licenseNumber: 'RN-005-2018',
        experience: '11 years',
        education: 'BSc Nursing, Bayero University',
        role: 'nurse',
        status: 'active',
        lastLogin: null,
        specialization: 'Maternity Nursing'
    }
};

// Updated nurse credentials - use the nurseAccounts above for login

document.addEventListener('DOMContentLoaded', function() {
    initializeNurseLogin();
});

function initializeNurseLogin() {
    const loginForm = document.getElementById('nurseLoginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleNurseLogin);
    }

    // Add sample credentials display
    displaySampleCredentials();
}

function displaySampleCredentials() {
    const credentialsContainer = document.getElementById('sampleCredentials');
    if (credentialsContainer) {
        credentialsContainer.innerHTML = `
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Sample Nurse Credentials</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Nurse ID:</strong> NUR001<br>
                        <strong>Password:</strong> nurse123<br>
                        <strong>Name:</strong> Nurse Amina Suleiman
                    </div>
                    <div class="col-md-6">
                        <strong>Department:</strong> General Ward<br>
                        <strong>Shift:</strong> Day Shift<br>
                        <strong>Status:</strong> Active
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <strong>Other Available Nurses:</strong> NUR002, NUR003, NUR004, NUR005 (all use password: nurse123)
                </small>
            </div>
        `;
    }
}

function handleNurseLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const nurseId = formData.get('nurseId').trim().toUpperCase();
    const password = formData.get('loginPassword').trim();
    const rememberMe = formData.get('rememberMe');
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate API call delay
    setTimeout(() => {
        if (validateNurseCredentials(nurseId, password)) {
            // Successful login
            const nurseData = nurseAccounts[nurseId];
            nurseData.loginTime = new Date().toISOString();
            
            // Store user data
            const storage = rememberMe ? localStorage : sessionStorage;
            storage.setItem('hospitalUser', JSON.stringify(nurseData));
            
            // Show success message
            showSuccessAlert('Login successful! Redirecting to nurse dashboard...');
            
            // Redirect to nurse dashboard (you may need to create this)
            setTimeout(() => {
                window.location.href = 'nurse-dashboard.html';
            }, 1500);
            
        } else {
            // Failed login
            showErrorAlert('Invalid Nurse ID or password. Please try again.');
            form.classList.add('was-validated');
        }

        hideLoading();
    }, 1000);
}

function validateNurseCredentials(nurseId, password) {
    const nurse = nurseAccounts[nurseId];
    return nurse && nurse.password === password && nurse.status === 'active';
}

function showErrorMessage(message) {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

function showSuccessMessage(message) {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

function clearErrorMessages() {
    const errorContainer = document.getElementById('errorMessage');
    if (errorContainer) {
        errorContainer.innerHTML = '';
    }
}

// Utility functions
function getAllNurses() {
    return nurseAccounts;
}

function isLoggedInAsNurse() {
    const user = JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser') || '{}');
    return user.role === 'nurse' && user.status === 'active';
}

function getCurrentNurse() {
    if (isLoggedInAsNurse()) {
        return JSON.parse(sessionStorage.getItem('hospitalUser') || localStorage.getItem('hospitalUser'));
    }
    return null;
}

function logoutNurse() {
    sessionStorage.removeItem('hospitalUser');
    localStorage.removeItem('hospitalUser');
    window.location.href = 'nurse-login.html';
}

// Make functions globally available
window.getAllNurses = getAllNurses;
window.isLoggedInAsNurse = isLoggedInAsNurse;
window.getCurrentNurse = getCurrentNurse;
window.logoutNurse = logoutNurse;
window.validateNurseCredentials = validateNurseCredentials;

// Missing utility functions
function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing in...';
    button.disabled = true;

    return function hideLoading() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

function showSuccessAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function showErrorAlert(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

function checkExistingSession() {
    const userData = localStorage.getItem('hospitalUser') || sessionStorage.getItem('hospitalUser');
    
    if (userData) {
        const user = JSON.parse(userData);
        if (user.role === 'nurse') {
            // User is already logged in as nurse
            showSuccessAlert('You are already logged in. Redirecting...');
            setTimeout(() => {
                window.location.href = 'nurse-dashboard.html';
            }, 1500);
        }
    }
}

function resetPassword() {
    const nurseId = document.getElementById('resetNurseId').value.trim().toUpperCase();
    const email = document.getElementById('resetEmail').value.trim();
    
    if (!nurseId || !email) {
        showErrorAlert('Please fill in all fields.');
        return;
    }
    
    if (!validateEmail(email)) {
        showErrorAlert('Please enter a valid email address.');
        return;
    }
    
    // Check if nurse exists
    if (!nurseCredentials[nurseId]) {
        showErrorAlert('Nurse ID not found.');
        return;
    }
    
    // Check if email matches
    if (nurseCredentials[nurseId].email !== email) {
        showErrorAlert('Email does not match our records.');
        return;
    }
    
    // Simulate sending reset email
    showSuccessAlert('Password reset link has been sent to your email address.');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
    modal.hide();
    
    // Clear form
    document.getElementById('forgotPasswordForm').reset();
}

// Utility functions
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function showSuccessAlert(message) {
    const alert = document.getElementById('successAlert');
    const messageSpan = document.getElementById('successMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showErrorAlert(message) {
    const alert = document.getElementById('errorAlert');
    const messageSpan = document.getElementById('errorMessage');
    
    if (alert && messageSpan) {
        messageSpan.textContent = message;
        alert.classList.add('show');
        
        setTimeout(() => {
            alert.classList.remove('show');
        }, 5000);
    }
}

function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Make resetPassword function globally available
window.resetPassword = resetPassword;
