// Patient Registration JavaScript

// Storage for registered patients (in a real app, this would be a database)
let registeredPatients = JSON.parse(localStorage.getItem('registeredPatients')) || {};

// Load existing patient credentials from patient-login.js
const existingPatients = {
    'PT001': { 
        password: 'patient123', 
        name: '<PERSON>', 
        email: '<EMAIL>',
        phone: '+234-************',
        dateOfBirth: '1990-05-15'
    },
    'PT002': { 
        password: 'patient456', 
        name: '<PERSON>', 
        email: '<EMAIL>',
        phone: '+234-************',
        dateOfBirth: '1985-08-22'
    },
    'PT003': { 
        password: 'patient789', 
        name: '<PERSON>', 
        email: 'micha<PERSON>.<EMAIL>',
        phone: '+234-************',
        dateOfBirth: '1992-12-10'
    }
};

// Merge existing patients with registered patients
Object.assign(registeredPatients, existingPatients);

document.addEventListener('DOMContentLoaded', function() {
    console.log('Patient registration JavaScript loaded');
    initializePatientRegistration();
});

function initializePatientRegistration() {
    console.log('Initializing patient registration...');
    const registrationForm = document.getElementById('patientRegistrationForm');

    if (registrationForm) {
        console.log('Registration form found, adding event listener');
        registrationForm.addEventListener('submit', handlePatientRegistration);
    } else {
        console.error('Registration form not found!');
    }

    // Add real-time password confirmation validation
    const confirmPasswordField = document.getElementById('confirmPassword');
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }

    // Add email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', validateEmailUnique);
    }

    console.log('Patient registration initialization complete');
}

function handlePatientRegistration(event) {
    event.preventDefault();
    console.log('Registration form submitted');

    const form = event.target;
    const formData = new FormData(form);

    // Debug: Log form data
    console.log('Form data entries:');
    for (let [key, value] of formData.entries()) {
        console.log(key + ': ' + value);
    }

    // Validate form
    if (!form.checkValidity()) {
        console.log('Form validation failed');
        form.classList.add('was-validated');
        showErrorAlert('Please fill in all required fields correctly.');
        return;
    }

    console.log('Form validation passed');
    
    // Validate password match
    if (!validatePasswordMatch()) {
        showErrorAlert('Passwords do not match.');
        return;
    }
    
    // Check if email is already registered
    const email = formData.get('email').trim().toLowerCase();
    if (isEmailAlreadyRegistered(email)) {
        showErrorAlert('This email address is already registered. Please use a different email or try logging in.');
        return;
    }

    // Get matric number as patient ID
    const matricNumber = formData.get('matricNumber').trim().toUpperCase();

    // Check if matric number is already registered
    if (isMatricNumberAlreadyRegistered(matricNumber)) {
        showErrorAlert('This matric number is already registered. Please check your matric number or try logging in.');
        return;
    }

    // Validate matric number format
    if (!validateMatricNumberFormat(matricNumber)) {
        showErrorAlert('Please enter a valid matric number in format: 22B/UE/BSE/1005');
        return;
    }
    
    // Create patient data object
    const patientData = {
        id: matricNumber,
        matricNumber: matricNumber,
        name: formData.get('fullName').trim(),
        dateOfBirth: formData.get('dateOfBirth'),
        gender: formData.get('gender'),
        department: formData.get('department'),
        bloodGroup: formData.get('bloodGroup'),
        email: email,
        phone: formData.get('phone').trim(),
        password: formData.get('password'),
        registrationDate: new Date().toISOString(),
        status: 'active',
        userType: 'student'
    };
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate registration process
    setTimeout(() => {
        try {
            // Save patient data using matric number as key
            registeredPatients[matricNumber] = patientData;
            localStorage.setItem('registeredPatients', JSON.stringify(registeredPatients));
            
            // Show success message
            showSuccessAlert('Registration successful! You can now login with your credentials.');
            
            // Show registration details modal
            showRegistrationSuccessModal(patientData);
            
            // Reset form
            form.reset();
            form.classList.remove('was-validated');
            
        } catch (error) {
            showErrorAlert('Registration failed. Please try again.');
        }
        
        hideLoading();
    }, 2000);
}

function validateMatricNumberFormat(matricNumber) {
    // Validate multiple matric number formats
    const matricPatterns = [
        /^[0-9]{2}[AB]\/UE\/BSE\/[0-9]{4}$/,    // 22A/UE/BSE/1001, 22B/UE/BSE/1005
        /^[0-9]{2}[AB]\/UE\/BNS\/[0-9]{4}$/,    // 22A/UE/BNS/1001, 22B/UE/BNS/1001
        /^[0-9]{2}[AB]\/UE\/LLB\/[0-9]{4}$/,    // 22A/UE/LLB/1001, 22B/UE/LLB/1001
        /^[0-9]{2}[AB]\/UE\/ENG\/[0-9]{4}$/,    // Engineering
        /^[0-9]{2}[AB]\/UE\/EDU\/[0-9]{4}$/,    // Education
        /^[0-9]{2}[AB]\/UE\/AGR\/[0-9]{4}$/,    // Agriculture
        /^[0-9]{2}[AB]\/UE\/ENV\/[0-9]{4}$/,    // Environmental Sciences
        /^[0-9]{2}[AB]\/UE\/ICT\/[0-9]{4}$/,    // ICT
        /^[0-9]{2}[AB]$/,                       // Short format: 23A, 23B, 24A
    ];

    return matricPatterns.some(pattern => pattern.test(matricNumber));
}

function isMatricNumberAlreadyRegistered(matricNumber) {
    return Object.keys(registeredPatients).includes(matricNumber);
}

function isEmailAlreadyRegistered(email) {
    return Object.values(registeredPatients).some(patient => 
        patient.email.toLowerCase() === email.toLowerCase()
    );
}

function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmPasswordField = document.getElementById('confirmPassword');
    
    if (password !== confirmPassword) {
        confirmPasswordField.setCustomValidity('Passwords do not match');
        confirmPasswordField.classList.add('is-invalid');
        return false;
    } else {
        confirmPasswordField.setCustomValidity('');
        confirmPasswordField.classList.remove('is-invalid');
        return true;
    }
}

function validateEmailUnique() {
    const emailField = document.getElementById('email');
    const email = emailField.value.trim().toLowerCase();
    
    if (email && isEmailAlreadyRegistered(email)) {
        emailField.setCustomValidity('This email is already registered');
        emailField.classList.add('is-invalid');
        return false;
    } else {
        emailField.setCustomValidity('');
        emailField.classList.remove('is-invalid');
        return true;
    }
}

function showRegistrationSuccessModal(patientData) {
    const modalHTML = `
        <div class="modal fade" id="registrationSuccessModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-check-circle me-2"></i>Registration Successful!
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-check fa-4x text-success mb-3"></i>
                            <h4>Welcome to Newgate Hospital!</h4>
                            <p class="text-muted">Your account has been created successfully.</p>
                        </div>
                        
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-primary">Your Login Credentials:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Matric Number:</strong> <span class="text-primary">${patientData.matricNumber}</span></p>
                                        <p><strong>Name:</strong> ${patientData.name}</p>
                                        <p><strong>Department:</strong> ${patientData.department}</p>
                                        <p><strong>Email:</strong> ${patientData.email}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Phone:</strong> ${patientData.phone}</p>
                                        <p><strong>Blood Group:</strong> ${patientData.bloodGroup}</p>
                                        <p><strong>Registration Date:</strong> ${new Date(patientData.registrationDate).toLocaleDateString()}</p>
                                    </div>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Important:</strong> Use your Matric Number (${patientData.matricNumber}) to login.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success" onclick="redirectToLogin()">
                            <i class="fas fa-sign-in-alt me-2"></i>Login Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if present
    const existingModal = document.getElementById('registrationSuccessModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('registrationSuccessModal'));
    modal.show();
}

function redirectToLogin() {
    window.location.href = 'patient-login.html';
}

// Utility functions
function showSuccessAlert(message) {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function showErrorAlert(message) {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-danger border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', toastHTML);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Creating Account...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// Test function for debugging
function testRegistration() {
    console.log('=== Registration Test ===');
    const form = document.getElementById('patientRegistrationForm');

    if (!form) {
        console.error('Form not found!');
        alert('Form not found! Check console for details.');
        return;
    }

    console.log('Form found:', form);

    // Check if form has required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'password'];
    const missingFields = [];

    requiredFields.forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (!field) {
            missingFields.push(fieldName);
        } else {
            console.log(`${fieldName}: ${field.value || 'EMPTY'}`);
        }
    });

    if (missingFields.length > 0) {
        console.error('Missing fields:', missingFields);
        alert('Missing fields: ' + missingFields.join(', '));
        return;
    }

    // Test form data collection
    const formData = new FormData(form);
    console.log('Form data entries:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // Test validation
    if (form.checkValidity()) {
        console.log('Form validation: PASSED');
        alert('Form validation passed! Check console for details.');
    } else {
        console.log('Form validation: FAILED');
        alert('Form validation failed! Check console for details.');
    }
}

// Make functions globally available
window.redirectToLogin = redirectToLogin;
window.testRegistration = testRegistration;
