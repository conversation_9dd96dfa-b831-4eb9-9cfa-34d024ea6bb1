<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Patient Login - Newgate Hospital</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-test-tube me-2"></i>Patient Login Test Page</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Test Instructions</h5>
                            <p class="mb-2">Use these credentials to test the patient login:</p>
                            <ul class="mb-0">
                                <li><strong>Patient ID:</strong> PT001</li>
                                <li><strong>Password:</strong> patient123</li>
                            </ul>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Available Test Accounts:</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Patient ID</th>
                                                <th>Password</th>
                                                <th>Name</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>PT001</code></td>
                                                <td><code>patient123</code></td>
                                                <td>John Doe</td>
                                            </tr>
                                            <tr>
                                                <td><code>PT002</code></td>
                                                <td><code>patient456</code></td>
                                                <td>Jane Smith</td>
                                            </tr>
                                            <tr>
                                                <td><code>PT003</code></td>
                                                <td><code>patient789</code></td>
                                                <td>Michael Johnson</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>Quick Test Form:</h5>
                                <form id="testForm">
                                    <div class="mb-3">
                                        <label for="testPatientId" class="form-label">Patient ID</label>
                                        <input type="text" class="form-control" id="testPatientId" value="PT001">
                                    </div>
                                    <div class="mb-3">
                                        <label for="testPassword" class="form-label">Password</label>
                                        <input type="text" class="form-control" id="testPassword" value="patient123">
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="testLogin()">
                                        <i class="fas fa-test me-2"></i>Test Login
                                    </button>
                                </form>
                                
                                <div id="testResult" class="mt-3"></div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="patient-login.html" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Go to Patient Login Page
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Patient credentials for testing
        const patientCredentials = {
            'PT001': { 
                password: 'patient123', 
                name: 'John Doe', 
                email: '<EMAIL>',
                phone: '+234-************',
                dateOfBirth: '1990-05-15'
            },
            'PT002': { 
                password: 'patient456', 
                name: 'Jane Smith', 
                email: '<EMAIL>',
                phone: '+234-************',
                dateOfBirth: '1985-08-22'
            },
            'PT003': { 
                password: 'patient789', 
                name: 'Michael Johnson', 
                email: '<EMAIL>',
                phone: '+234-************',
                dateOfBirth: '1992-12-10'
            }
        };

        function testLogin() {
            const patientId = document.getElementById('testPatientId').value.trim().toUpperCase();
            const password = document.getElementById('testPassword').value.trim();
            const resultDiv = document.getElementById('testResult');
            
            console.log('Testing login with:', patientId, password);
            
            if (patientCredentials[patientId] && patientCredentials[patientId].password === password) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Login Test Successful!</strong><br>
                        Welcome, ${patientCredentials[patientId].name}!<br>
                        <small>Email: ${patientCredentials[patientId].email}</small>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        <strong>Login Test Failed!</strong><br>
                        Invalid Patient ID or password.
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
